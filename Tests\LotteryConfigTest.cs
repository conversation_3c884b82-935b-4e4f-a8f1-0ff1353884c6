using OneChatGoRobot.Config;
using OneChatGoRobot.Enum;

namespace OneChatGoRobot.Tests;

/// <summary>
/// 彩种配置测试类
/// 用于验证彩种配置的统一性和正确性
/// </summary>
public static class LotteryConfigTest
{
    /// <summary>
    /// 测试彩种配置的基本功能
    /// </summary>
    public static void TestLotteryConfig()
    {
        Console.WriteLine("=== 彩种配置测试开始 ===");

        // 测试默认彩种
        Console.WriteLine($"默认彩种: {AppConfig.Lottery.CurrentLottery}");

        // 测试彩种切换
        var originalLottery = AppConfig.Lottery.CurrentLottery;
        
        AppConfig.Lottery.CurrentLottery = EnumLottery.一六八飞艇;
        Console.WriteLine($"切换后彩种: {AppConfig.Lottery.CurrentLottery}");

        // 测试彩种配置获取
        var config = AppConfig.Lottery.GetConfig(AppConfig.Lottery.CurrentLottery);
        Console.WriteLine($"彩种名称: {config.Name}");
        Console.WriteLine($"每期时长: {config.PeriodDuration}秒");
        Console.WriteLine($"封盘时间: {config.CloseDuration}秒");
        Console.WriteLine($"每日期数: {config.DailyPeriods}期");
        Console.WriteLine($"开始时间: {config.StartTime}");

        // 恢复原始彩种
        AppConfig.Lottery.CurrentLottery = originalLottery;
        Console.WriteLine($"恢复彩种: {AppConfig.Lottery.CurrentLottery}");

        Console.WriteLine("=== 彩种配置测试完成 ===");
    }

    /// <summary>
    /// 测试所有彩种的配置
    /// </summary>
    public static void TestAllLotteryConfigs()
    {
        Console.WriteLine("=== 所有彩种配置测试 ===");

        var lotteries = new[] { EnumLottery.台湾宾果, EnumLottery.一六八飞艇 };

        foreach (var lottery in lotteries)
        {
            try
            {
                var config = AppConfig.Lottery.GetConfig(lottery);
                Console.WriteLine($"彩种: {lottery}");
                Console.WriteLine($"  名称: {config.Name}");
                Console.WriteLine($"  每期时长: {config.PeriodDuration}秒");
                Console.WriteLine($"  封盘时间: {config.CloseDuration}秒");
                Console.WriteLine($"  每日期数: {config.DailyPeriods}期");
                Console.WriteLine($"  开始时间: {config.StartTime}");
                Console.WriteLine();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"彩种 {lottery} 配置获取失败: {ex.Message}");
            }
        }

        Console.WriteLine("=== 所有彩种配置测试完成 ===");
    }
}
