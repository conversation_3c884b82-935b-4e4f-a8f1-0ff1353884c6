# OneChatGoRobot 用户使用指南

## 🎯 程序简介

OneChatGoRobot 是一个智能聊天室机器人程序，支持自动上分和下注功能。最新版本增加了启动时彩种选择功能，提供更好的用户体验。

## 🚀 快速开始

### 1. 程序启动
双击 `OneChatGoRobot.exe` 或在命令行运行：
```bash
dotnet run
```

### 2. 彩种选择
程序启动后会自动显示彩种选择窗口：

```
┌──────────────────────────────────────────┐
│  🎯  OneChatGoRobot - 彩种选择           │
├──────────────────────────────────────────┤
│  请选择彩种：                            │
│  ● 台湾宾果        ○ 一六八飞艇          │
├──────────────────────────────────────────┤
│  彩种信息：                              │
│  名称：台湾宾果                          │
│  每期时长：300秒                         │
│  每日期数：203期                         │
│  开始时间：07:00                         │
├──────────────────────────────────────────┤
│                        [确定]  [取消]   │
└──────────────────────────────────────────┘
```

### 3. 选择操作
- **鼠标操作**：点击单选按钮选择彩种
- **键盘操作**：
  - `Tab` - 切换焦点
  - `空格` - 选择/取消选择
  - `Enter` - 确认选择
  - `ESC` - 取消选择

### 4. 确认启动
- 点击 **确定** 按钮或按 `Enter` 键确认选择
- 点击 **取消** 按钮或按 `ESC` 键退出程序

## 🎮 彩种介绍

### 台湾宾果
- **名称**：台湾宾果
- **每期时长**：300秒（5分钟）
- **每日期数**：203期
- **开始时间**：07:00
- **期号格式**：民国年份 + 6位序号
- **特点**：经典彩种，期数多，时间长

### 一六八飞艇
- **名称**：一六八飞艇
- **每期时长**：240秒（4分钟）
- **每日期数**：180期
- **开始时间**：13:05
- **期号格式**：日期 + 3位序号
- **特点**：快节奏彩种，期数适中

## 🖥️ 主程序界面

选择彩种后，主程序界面将显示：

```
┌──────────────────────────────────────────┐
│ OneChatGoRobot - 聊天室机器人            │
├──────────────────────────────────────────┤
│ [UID输入框________________] [添加用户]   │
├──────────────────────────────────────────┤
│ ┌──────────────────────────────────────┐ │
│ │ UID    │ 昵称    │ 状态    │ 操作   │ │
│ │────────┼─────────┼─────────┼────────│ │
│ │        │         │         │        │ │
│ └──────────────────────────────────────┘ │
├──────────────────────────────────────────┤
│                 [开始]                   │
├──────────────────────────────────────────┤
│ 日志输出：                               │
│ [12:34:56] 当前彩种：台湾宾果            │
│ [12:34:56] 彩种配置：每期300秒，每日203期│
│ [12:34:57] 数据库初始化完成              │
└──────────────────────────────────────────┘
```

## 📝 使用步骤

### 第一步：添加用户
1. 在UID输入框中输入用户ID
2. 点击"添加用户"按钮
3. 系统会自动获取用户信息并添加到列表

### 第二步：启动机器人
1. 确保已添加至少一个用户
2. 点击"开始"按钮启动机器人
3. 观察日志输出，确认机器人正常工作

### 第三步：监控运行
- 查看日志了解机器人运行状态
- 监控用户列表中的用户状态
- 根据需要添加或删除用户

## ⚙️ 功能特性

### 自动上分
- 程序启动时为所有用户执行一次性上分
- 上分金额：5000-20000元随机
- 模拟真实用户充值行为

### 智能下注
- **下注时机**：封盘前160秒内开始
- **下注策略**：
  - 番类：1番、2番、3番、4番
  - 正类：1正、2正、3正、4正
  - 单双类：单、双
- **下注金额**：5-300元随机
- **防重复**：同一用户同一期不重复下注

### 实时监控
- 24小时监控期号状态
- 实时显示封盘倒计时
- 自动生成期号时间数据

## 🔧 高级设置

### 数据库管理
- 程序使用SQLite数据库存储用户信息
- 数据库文件：`OneChatGoRobot.db`
- 支持数据备份和恢复

### 日志系统
- 多级别日志记录（调试、信息、警告、错误）
- 实时日志显示
- 日志文件自动管理

### 配置管理
- 集中化配置管理
- 支持彩种配置
- 可扩展的设置选项

## ❗ 注意事项

### 使用前准备
1. 确保网络连接正常
2. 确保聊天室服务器可访问
3. 准备有效的用户UID

### 运行时注意
1. 不要同时运行多个程序实例
2. 保持网络连接稳定
3. 定期检查日志输出

### 安全提醒
1. 妥善保管用户信息
2. 定期备份数据库
3. 注意程序运行环境安全

## 🐛 常见问题

### Q: 程序启动后没有显示彩种选择窗口？
A: 检查程序是否正常启动，尝试重新运行程序。

### Q: 选择彩种后主程序没有启动？
A: 确保点击了"确定"按钮，检查是否有错误提示。

### Q: 添加用户时提示错误？
A: 检查UID是否有效，确保网络连接正常。

### Q: 机器人不下注？
A: 检查是否在正确的时间段，确保用户列表不为空。

### Q: 程序运行缓慢？
A: 检查网络连接，重启程序，清理数据库。

## 📞 技术支持

### 系统要求
- **操作系统**：Windows 10/11
- **框架**：.NET 8.0
- **内存**：至少2GB RAM
- **存储**：至少100MB可用空间

### 依赖组件
- FreeSql ORM框架
- Newtonsoft.Json
- RestSharp HTTP客户端
- SQLite数据库

### 联系方式
如遇到技术问题，请：
1. 查看程序日志输出
2. 检查网络连接状态
3. 确认配置文件正确
4. 重启程序尝试解决

## 🎉 版本更新

### 最新版本特性
- ✨ 启动时彩种选择功能
- 🎨 优化的用户界面设计
- ⚡ 提升的程序性能
- 🔧 改进的错误处理
- 📚 完善的文档说明

### 更新内容
1. **新增启动时彩种选择窗口**
2. **简化主程序界面设计**
3. **优化程序启动流程**
4. **提升用户体验**
5. **增强程序稳定性**

---

**祝您使用愉快！** 🎊

如有任何问题或建议，欢迎反馈！
