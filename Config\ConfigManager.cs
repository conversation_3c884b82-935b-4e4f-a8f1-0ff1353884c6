using Newtonsoft.Json;
using System.Diagnostics;

namespace OneChatGoRobot.Config;

/// <summary>
/// 配置管理器
/// 负责配置文件的加载、保存、验证和管理
/// </summary>
public static class ConfigManager
{
    /// <summary>
    /// 配置文件路径
    /// </summary>
    private static readonly string ConfigFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bot-config.json");

    /// <summary>
    /// 当前配置实例
    /// </summary>
    public static BotConfig Current { get; private set; } = new();

    /// <summary>
    /// 配置是否已加载
    /// </summary>
    public static bool IsLoaded { get; private set; } = false;

    /// <summary>
    /// 加载配置文件
    /// 如果文件不存在，则创建默认配置文件
    /// </summary>
    /// <returns>是否加载成功</returns>
    public static async Task<bool> LoadConfigAsync()
    {
        try
        {
            if (File.Exists(ConfigFilePath))
            {
                // 读取现有配置文件
                string json = await File.ReadAllTextAsync(ConfigFilePath);
                var config = JsonConvert.DeserializeObject<BotConfig>(json);
                
                if (config != null)
                {
                    Current = config;
                    ValidateAndFixConfig();
                    IsLoaded = true;
                    Debug.WriteLine("配置文件加载成功");
                    return true;
                }
            }
            
            // 文件不存在或解析失败，创建默认配置
            await CreateDefaultConfigAsync();
            IsLoaded = true;
            Debug.WriteLine("创建默认配置文件");
            return true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"加载配置文件失败: {ex.Message}");
            
            // 加载失败时使用默认配置
            Current = new BotConfig();
            ValidateAndFixConfig();
            IsLoaded = true;
            return false;
        }
    }

    /// <summary>
    /// 保存配置文件
    /// </summary>
    /// <returns>是否保存成功</returns>
    public static async Task<bool> SaveConfigAsync()
    {
        try
        {
            ValidateAndFixConfig();
            
            string json = JsonConvert.SerializeObject(Current, Formatting.Indented);
            await File.WriteAllTextAsync(ConfigFilePath, json);
            
            Debug.WriteLine("配置文件保存成功");
            return true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"保存配置文件失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 创建默认配置文件
    /// </summary>
    private static async Task CreateDefaultConfigAsync()
    {
        Current = new BotConfig();
        ValidateAndFixConfig();
        await SaveConfigAsync();
    }

    /// <summary>
    /// 验证和修复配置
    /// 确保所有配置值都在合理范围内
    /// </summary>
    private static void ValidateAndFixConfig()
    {
        // 验证上分配置
        if (Current.TopUp.MinAmount < 1000)
            Current.TopUp.MinAmount = 1000;
        if (Current.TopUp.MaxAmount < Current.TopUp.MinAmount)
            Current.TopUp.MaxAmount = Current.TopUp.MinAmount + 5000;
        if (Current.TopUp.AmountStep < 100)
            Current.TopUp.AmountStep = 100;

        // 验证投注配置
        if (Current.Betting.MinAmount < 1)
            Current.Betting.MinAmount = 1;
        if (Current.Betting.MaxAmount < Current.Betting.MinAmount)
            Current.Betting.MaxAmount = Current.Betting.MinAmount + 10;
        if (Current.Betting.AmountStep < 1)
            Current.Betting.AmountStep = 1;

        // 验证时机配置
        if (Current.Timing.StartBettingSeconds < 10)
            Current.Timing.StartBettingSeconds = 10;
        if (Current.Timing.StartBettingSeconds > 300)
            Current.Timing.StartBettingSeconds = 300;
        if (Current.Timing.StatusCheckInterval < 500)
            Current.Timing.StatusCheckInterval = 500;
        if (Current.Timing.MinWaitTime < 100)
            Current.Timing.MinWaitTime = 100;
        if (Current.Timing.MaxWaitTime < Current.Timing.MinWaitTime)
            Current.Timing.MaxWaitTime = Current.Timing.MinWaitTime + 1000;

        // 验证服务器配置
        if (string.IsNullOrWhiteSpace(Current.Server.Url))
            Current.Server.Url = "http://127.0.0.1:3001";
        if (Current.Server.TimeoutSeconds < 1)
            Current.Server.TimeoutSeconds = 1;
        if (Current.Server.TimeoutSeconds > 30)
            Current.Server.TimeoutSeconds = 30;

        // 验证行为配置
        if (Current.Behavior.LogLevel < 0)
            Current.Behavior.LogLevel = 0;
        if (Current.Behavior.LogLevel > 2)
            Current.Behavior.LogLevel = 2;

        // 验证投注策略配置
        if (Current.Betting.Strategy.FanOptions == null || Current.Betting.Strategy.FanOptions.Count == 0)
            Current.Betting.Strategy.FanOptions = new List<string> { "1番", "2番", "3番", "4番" };
        if (Current.Betting.Strategy.ZhengOptions == null || Current.Betting.Strategy.ZhengOptions.Count == 0)
            Current.Betting.Strategy.ZhengOptions = new List<string> { "1正", "2正", "3正", "4正" };
        if (Current.Betting.Strategy.OddEvenOptions == null || Current.Betting.Strategy.OddEvenOptions.Count == 0)
            Current.Betting.Strategy.OddEvenOptions = new List<string> { "单", "双" };
    }

    /// <summary>
    /// 重置为默认配置
    /// </summary>
    public static async Task ResetToDefaultAsync()
    {
        Current = new BotConfig();
        ValidateAndFixConfig();
        await SaveConfigAsync();
        Debug.WriteLine("配置已重置为默认值");
    }

    /// <summary>
    /// 获取配置文件路径
    /// </summary>
    /// <returns>配置文件完整路径</returns>
    public static string GetConfigFilePath()
    {
        return ConfigFilePath;
    }

    /// <summary>
    /// 检查配置文件是否存在
    /// </summary>
    /// <returns>配置文件是否存在</returns>
    public static bool ConfigFileExists()
    {
        return File.Exists(ConfigFilePath);
    }

    /// <summary>
    /// 生成投注金额列表
    /// 根据配置生成可用的投注金额选项
    /// </summary>
    /// <returns>投注金额字符串列表</returns>
    public static List<string> GenerateBettingAmounts()
    {
        var amounts = new List<string>();
        
        for (int amount = Current.Betting.MinAmount; 
             amount <= Current.Betting.MaxAmount; 
             amount += Current.Betting.AmountStep)
        {
            amounts.Add(amount.ToString());
        }
        
        return amounts;
    }

    /// <summary>
    /// 生成随机上分金额
    /// 根据配置生成符合规则的随机上分金额
    /// </summary>
    /// <returns>上分金额</returns>
    public static int GenerateRandomTopUpAmount()
    {
        var random = new Random();
        int steps = (Current.TopUp.MaxAmount - Current.TopUp.MinAmount) / Current.TopUp.AmountStep + 1;
        int randomStep = random.Next(steps);
        return Current.TopUp.MinAmount + (randomStep * Current.TopUp.AmountStep);
    }

    /// <summary>
    /// 获取配置摘要信息
    /// 用于日志显示和调试
    /// </summary>
    /// <returns>配置摘要字符串</returns>
    public static string GetConfigSummary()
    {
        return $"配置摘要:\n" +
               $"  上分: {Current.TopUp.MinAmount}-{Current.TopUp.MaxAmount}元 (步长{Current.TopUp.AmountStep})\n" +
               $"  投注: {Current.Betting.MinAmount}-{Current.Betting.MaxAmount}元 (步长{Current.Betting.AmountStep})\n" +
               $"  时机: 封盘前{Current.Timing.StartBettingSeconds}秒开始投注\n" +
               $"  等待: {Current.Timing.MinWaitTime}-{Current.Timing.MaxWaitTime}毫秒\n" +
               $"  服务器: {Current.Server.Url}";
    }
}
