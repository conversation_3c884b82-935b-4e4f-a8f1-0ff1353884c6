﻿using FreeSql.DataAnnotations;

namespace OneChatGoRobot.Models;

/// <summary>
/// 彩种期号时间模型
/// 用于存储彩种的期号和开奖时间信息
/// </summary>
[Table(Name = "IssueTime")]
public class IssueTime
{
    /// <summary>
    /// 主键标识
    /// 自增主键，唯一标识一条期号记录
    /// </summary>
    [Column(IsPrimary = true, IsIdentity = true)]
    public int Id { get; set; }

    /// <summary>
    /// 期号
    /// 彩种的期号编号，例如“112000001”
    /// </summary>
    public string Issue { get; set; } = string.Empty;

    /// <summary>
    /// 开奖时间
    /// 该期彩种的开奖时间
    /// </summary>
    public DateTime OpenTime { get; set; }
}