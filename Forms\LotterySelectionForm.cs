using OneChatGoRobot.Config;
using OneChatGoRobot.Enum;

namespace OneChatGoRobot.Forms;

/// <summary>
/// 彩种选择启动窗口
/// 在程序启动时显示，让用户选择要使用的彩种
/// </summary>
public partial class LotterySelectionForm : Form
{
    /// <summary>
    /// 用户选择的彩种
    /// </summary>
    public EnumLottery SelectedLottery { get; private set; } = EnumLottery.台湾宾果;

    /// <summary>
    /// 用户是否确认选择（点击确定按钮）
    /// </summary>
    public bool IsConfirmed { get; private set; } = false;

    public LotterySelectionForm()
    {
        InitializeComponent();
        InitializeLotteryOptions();
    }

    /// <summary>
    /// 初始化彩种选项
    /// </summary>
    private void InitializeLotteryOptions()
    {
        // 设置默认选择
        radioButton_TaiwanBingo.Checked = true;
        SelectedLottery = EnumLottery.台湾宾果;

        // 设置彩种信息
        UpdateLotteryInfo();
    }

    /// <summary>
    /// 更新彩种信息显示
    /// </summary>
    private void UpdateLotteryInfo()
    {
        var config = AppConfig.Lottery.GetConfig(SelectedLottery);
        
        label_LotteryInfo.Text = $"彩种信息：\n" +
                                $"名称：{config.Name}\n" +
                                $"每期时长：{config.PeriodDuration}秒\n" +
                                $"每日期数：{config.DailyPeriods}期\n" +
                                $"开始时间：{config.StartTime:hh\\:mm}";
    }

    /// <summary>
    /// 台湾宾果单选按钮选择事件
    /// </summary>
    private void radioButton_TaiwanBingo_CheckedChanged(object sender, EventArgs e)
    {
        if (radioButton_TaiwanBingo.Checked)
        {
            SelectedLottery = EnumLottery.台湾宾果;
            UpdateLotteryInfo();
        }
    }

    /// <summary>
    /// 一六八飞艇单选按钮选择事件
    /// </summary>
    private void radioButton_Flight168_CheckedChanged(object sender, EventArgs e)
    {
        if (radioButton_Flight168.Checked)
        {
            SelectedLottery = EnumLottery.一六八飞艇;
            UpdateLotteryInfo();
        }
    }

    /// <summary>
    /// 确定按钮点击事件
    /// </summary>
    private void button_OK_Click(object sender, EventArgs e)
    {
        IsConfirmed = true;
        AppConfig.Lottery.CurrentLottery = SelectedLottery;
        DialogResult = DialogResult.OK;
        Close();
    }

    /// <summary>
    /// 取消按钮点击事件
    /// </summary>
    private void button_Cancel_Click(object sender, EventArgs e)
    {
        IsConfirmed = false;
        DialogResult = DialogResult.Cancel;
        Close();
    }

    /// <summary>
    /// 窗体加载事件
    /// </summary>
    private void LotterySelectionForm_Load(object sender, EventArgs e)
    {
        // 设置窗体居中显示
        CenterToScreen();
        
        // 设置焦点到确定按钮
        button_OK.Focus();
    }

    /// <summary>
    /// 按键事件处理（支持回车确认，ESC取消）
    /// </summary>
    private void LotterySelectionForm_KeyDown(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode)
        {
            case Keys.Enter:
                button_OK_Click(sender, e);
                break;
            case Keys.Escape:
                button_Cancel_Click(sender, e);
                break;
        }
    }
}
