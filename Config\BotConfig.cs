using Newtonsoft.Json;

namespace OneChatGoRobot.Config;

/// <summary>
/// 机器人配置类
/// 管理所有可配置的参数，包括上分金额、投注金额、时机控制等
/// </summary>
public class BotConfig
{
    /// <summary>
    /// 上分配置
    /// </summary>
    public TopUpConfig TopUp { get; set; } = new();

    /// <summary>
    /// 投注配置
    /// </summary>
    public BettingConfig Betting { get; set; } = new();

    /// <summary>
    /// 时机控制配置
    /// </summary>
    public TimingConfig Timing { get; set; } = new();

    /// <summary>
    /// 服务器配置
    /// </summary>
    public ServerConfig Server { get; set; } = new();

    /// <summary>
    /// 行为配置
    /// </summary>
    public BehaviorConfig Behavior { get; set; } = new();
}

/// <summary>
/// 上分配置
/// 控制机器人的自动上分行为
/// </summary>
public class TopUpConfig
{
    /// <summary>
    /// 最小上分金额（元）
    /// </summary>
    public int MinAmount { get; set; } = 5000;

    /// <summary>
    /// 最大上分金额（元）
    /// </summary>
    public int MaxAmount { get; set; } = 20000;

    /// <summary>
    /// 上分金额步长（元）
    /// 上分金额必须是此步长的倍数
    /// </summary>
    public int AmountStep { get; set; } = 1000;

    /// <summary>
    /// 是否启用自动上分
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 上分消息格式
    /// {0} 为金额占位符
    /// </summary>
    public string MessageFormat { get; set; } = "上{0}";
}

/// <summary>
/// 投注配置
/// 控制机器人的自动投注行为
/// </summary>
public class BettingConfig
{
    /// <summary>
    /// 最小投注金额（元）
    /// </summary>
    public int MinAmount { get; set; } = 5;

    /// <summary>
    /// 最大投注金额（元）
    /// </summary>
    public int MaxAmount { get; set; } = 300;

    /// <summary>
    /// 投注金额步长（元）
    /// 投注金额必须是此步长的倍数
    /// </summary>
    public int AmountStep { get; set; } = 5;

    /// <summary>
    /// 是否启用自动投注
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 投注策略配置
    /// </summary>
    public BettingStrategyConfig Strategy { get; set; } = new();
}

/// <summary>
/// 投注策略配置
/// </summary>
public class BettingStrategyConfig
{
    /// <summary>
    /// 番类投注选项
    /// </summary>
    public List<string> FanOptions { get; set; } = new() { "1番", "2番", "3番", "4番" };

    /// <summary>
    /// 正类投注选项
    /// </summary>
    public List<string> ZhengOptions { get; set; } = new() { "1正", "2正", "3正", "4正" };

    /// <summary>
    /// 单双类投注选项
    /// </summary>
    public List<string> OddEvenOptions { get; set; } = new() { "单", "双" };

    /// <summary>
    /// 是否启用智能对立项目避免
    /// 避免同时投注对立的项目（如1正和3正）
    /// </summary>
    public bool EnableOpposingAvoidance { get; set; } = true;

    /// <summary>
    /// 对立项目组定义
    /// </summary>
    public List<List<string>> OpposingGroups { get; set; } = new()
    {
        new() { "1正", "3正" },
        new() { "2正", "4正" },
        new() { "单", "双" }
    };
}

/// <summary>
/// 时机控制配置
/// 控制机器人的操作时机
/// </summary>
public class TimingConfig
{
    /// <summary>
    /// 开始投注的时机（距离封盘秒数）
    /// 只有在封盘前这么多秒内才开始投注
    /// </summary>
    public int StartBettingSeconds { get; set; } = 160;

    /// <summary>
    /// 期号状态检查间隔（毫秒）
    /// 每隔这么多毫秒检查一次期号状态
    /// </summary>
    public int StatusCheckInterval { get; set; } = 2000;

    /// <summary>
    /// 用户操作最小等待时间（毫秒）
    /// 模拟人类操作的随机延迟
    /// </summary>
    public int MinWaitTime { get; set; } = 1000;

    /// <summary>
    /// 用户操作最大等待时间（毫秒）
    /// 模拟人类操作的随机延迟
    /// </summary>
    public int MaxWaitTime { get; set; } = 3000;
}

/// <summary>
/// 服务器配置
/// </summary>
public class ServerConfig
{
    /// <summary>
    /// 聊天室服务器URL
    /// </summary>
    public string Url { get; set; } = "http://127.0.0.1:3001";

    /// <summary>
    /// API请求超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 2;

    /// <summary>
    /// 获取用户信息API路径
    /// </summary>
    public string GetUserByUidPath { get; set; } = "/api/users/getUserByUid";

    /// <summary>
    /// 通过GUID获取用户信息API路径
    /// </summary>
    public string GetUserByGuidPath { get; set; } = "/api/users/getUserByGuid";

    /// <summary>
    /// 发送消息API路径
    /// </summary>
    public string SendMessagePath { get; set; } = "/api/messages/sendText";
}

/// <summary>
/// 行为配置
/// 控制机器人的行为特征
/// </summary>
public class BehaviorConfig
{
    /// <summary>
    /// 是否启用随机用户顺序
    /// 每次投注时是否随机打乱用户顺序
    /// </summary>
    public bool RandomizeUserOrder { get; set; } = true;

    /// <summary>
    /// 是否启用防重复投注
    /// 同一用户在同一期是否允许重复投注
    /// </summary>
    public bool PreventDuplicateBetting { get; set; } = true;

    /// <summary>
    /// 日志详细级别
    /// 0=基本, 1=详细, 2=调试
    /// </summary>
    public int LogLevel { get; set; } = 1;

    /// <summary>
    /// 是否在启动时显示配置信息
    /// </summary>
    public bool ShowConfigOnStartup { get; set; } = true;
}
