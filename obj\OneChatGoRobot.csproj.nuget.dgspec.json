{"format": 1, "restore": {"F:\\SolutionOneChat\\OneChatGoRobot\\OneChatGoRobot.csproj": {}}, "projects": {"F:\\SolutionOneChat\\OneChatGoRobot\\OneChatGoRobot.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\SolutionOneChat\\OneChatGoRobot\\OneChatGoRobot.csproj", "projectName": "OneChatGoRobot", "projectPath": "F:\\SolutionOneChat\\OneChatGoRobot\\OneChatGoRobot.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\SolutionOneChat\\OneChatGoRobot\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Costura.Fody": {"suppressParent": "All", "target": "Package", "version": "[6.0.0, )"}, "FreeSql": {"target": "Package", "version": "[3.5.206, )"}, "FreeSql.Provider.Sqlite": {"target": "Package", "version": "[3.5.206, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "RestSharp": {"target": "Package", "version": "[112.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}