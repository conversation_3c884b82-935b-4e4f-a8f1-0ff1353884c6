namespace OneChatGoRobot.Services;

/// <summary>
/// 任务状态枚举
/// </summary>
public enum TaskStatus
{
    /// <summary>
    /// 未启动
    /// </summary>
    NotStarted,
    
    /// <summary>
    /// 运行中
    /// </summary>
    Running,
    
    /// <summary>
    /// 已暂停
    /// </summary>
    Paused,
    
    /// <summary>
    /// 已停止
    /// </summary>
    Stopped,
    
    /// <summary>
    /// 出错
    /// </summary>
    Error
}

/// <summary>
/// 任务管理器
/// 统一管理所有后台任务的生命周期
/// </summary>
public class TaskManager : IDisposable
{
    private readonly Dictionary<string, ManagedTask> _tasks = new();
    private readonly ILogger _logger;
    private readonly object _lockObject = new();
    private bool _disposed = false;

    public TaskManager(ILogger logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 注册任务
    /// </summary>
    /// <param name="taskName">任务名称</param>
    /// <param name="taskFunc">任务函数</param>
    /// <param name="cancellationToken">取消令牌</param>
    public void RegisterTask(string taskName, Func<CancellationToken, Task> taskFunc, CancellationToken cancellationToken = default)
    {
        lock (_lockObject)
        {
            if (_tasks.ContainsKey(taskName))
            {
                throw new InvalidOperationException($"任务 '{taskName}' 已存在");
            }

            var managedTask = new ManagedTask(taskName, taskFunc, _logger);
            _tasks[taskName] = managedTask;
            
            _logger.LogInfo($"任务 '{taskName}' 已注册");
        }
    }

    /// <summary>
    /// 启动任务
    /// </summary>
    /// <param name="taskName">任务名称</param>
    public async Task StartTaskAsync(string taskName)
    {
        lock (_lockObject)
        {
            if (!_tasks.TryGetValue(taskName, out var task))
            {
                throw new ArgumentException($"任务 '{taskName}' 不存在");
            }

            if (task.Status == TaskStatus.Running)
            {
                _logger.LogWarning($"任务 '{taskName}' 已在运行中");
                return;
            }
        }

        await _tasks[taskName].StartAsync();
        _logger.LogInfo($"任务 '{taskName}' 已启动");
    }

    /// <summary>
    /// 停止任务
    /// </summary>
    /// <param name="taskName">任务名称</param>
    public async Task StopTaskAsync(string taskName)
    {
        lock (_lockObject)
        {
            if (!_tasks.TryGetValue(taskName, out var task))
            {
                throw new ArgumentException($"任务 '{taskName}' 不存在");
            }
        }

        await _tasks[taskName].StopAsync();
        _logger.LogInfo($"任务 '{taskName}' 已停止");
    }

    /// <summary>
    /// 停止所有任务
    /// </summary>
    public async Task StopAllTasksAsync()
    {
        var stopTasks = new List<Task>();
        
        lock (_lockObject)
        {
            foreach (var task in _tasks.Values)
            {
                if (task.Status == TaskStatus.Running)
                {
                    stopTasks.Add(task.StopAsync());
                }
            }
        }

        await Task.WhenAll(stopTasks);
        _logger.LogInfo("所有任务已停止");
    }

    /// <summary>
    /// 获取任务状态
    /// </summary>
    /// <param name="taskName">任务名称</param>
    /// <returns>任务状态</returns>
    public TaskStatus GetTaskStatus(string taskName)
    {
        lock (_lockObject)
        {
            return _tasks.TryGetValue(taskName, out var task) ? task.Status : TaskStatus.NotStarted;
        }
    }

    /// <summary>
    /// 获取所有任务状态
    /// </summary>
    /// <returns>任务状态字典</returns>
    public Dictionary<string, TaskStatus> GetAllTaskStatus()
    {
        lock (_lockObject)
        {
            return _tasks.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.Status);
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            StopAllTasksAsync().Wait(TimeSpan.FromSeconds(5));
            
            lock (_lockObject)
            {
                foreach (var task in _tasks.Values)
                {
                    task.Dispose();
                }
                _tasks.Clear();
            }
            
            _disposed = true;
        }
    }
}

/// <summary>
/// 托管任务
/// </summary>
internal class ManagedTask : IDisposable
{
    private readonly string _name;
    private readonly Func<CancellationToken, Task> _taskFunc;
    private readonly ILogger _logger;
    private CancellationTokenSource? _cancellationTokenSource;
    private Task? _runningTask;

    public TaskStatus Status { get; private set; } = TaskStatus.NotStarted;

    public ManagedTask(string name, Func<CancellationToken, Task> taskFunc, ILogger logger)
    {
        _name = name;
        _taskFunc = taskFunc;
        _logger = logger;
    }

    public async Task StartAsync()
    {
        if (Status == TaskStatus.Running)
            return;

        _cancellationTokenSource = new CancellationTokenSource();
        Status = TaskStatus.Running;

        _runningTask = Task.Run(async () =>
        {
            try
            {
                await _taskFunc(_cancellationTokenSource.Token);
                Status = TaskStatus.Stopped;
            }
            catch (OperationCanceledException)
            {
                Status = TaskStatus.Stopped;
                _logger.LogInfo($"任务 '{_name}' 被取消");
            }
            catch (Exception ex)
            {
                Status = TaskStatus.Error;
                _logger.LogException(ex, $"任务 '{_name}' 执行异常");
            }
        });

        await Task.Yield(); // 确保任务已开始
    }

    public async Task StopAsync()
    {
        if (Status != TaskStatus.Running)
            return;

        _cancellationTokenSource?.Cancel();
        
        if (_runningTask != null)
        {
            try
            {
                await _runningTask.WaitAsync(TimeSpan.FromSeconds(5));
            }
            catch (TimeoutException)
            {
                _logger.LogWarning($"任务 '{_name}' 停止超时");
            }
        }

        Status = TaskStatus.Stopped;
    }

    public void Dispose()
    {
        _cancellationTokenSource?.Cancel();
        _cancellationTokenSource?.Dispose();
        _runningTask?.Dispose();
    }
}
