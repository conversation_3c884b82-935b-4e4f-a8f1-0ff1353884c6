namespace OneChatGoRobot.Forms
{
    partial class ConfigForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tabControl_Main = new TabControl();
            tabPage_TopUp = new TabPage();
            groupBox_TopUp = new GroupBox();
            textBox_TopUpFormat = new TextBox();
            label_TopUpFormat = new Label();
            checkBox_TopUpEnabled = new CheckBox();
            numericUpDown_TopUpMax = new NumericUpDown();
            label_TopUpMax = new Label();
            numericUpDown_TopUpMin = new NumericUpDown();
            label_TopUpMin = new Label();
            tabPage_Betting = new TabPage();
            groupBox_Betting = new GroupBox();
            checkBox_BettingEnabled = new CheckBox();
            numericUpDown_BettingMax = new NumericUpDown();
            label_BettingMax = new Label();
            numericUpDown_BettingMin = new NumericUpDown();
            label_BettingMin = new Label();
            tabPage_Timing = new TabPage();
            groupBox_Timing = new GroupBox();
            numericUpDown_MaxWaitTime = new NumericUpDown();
            label_MaxWaitTime = new Label();
            numericUpDown_MinWaitTime = new NumericUpDown();
            label_MinWaitTime = new Label();
            numericUpDown_StatusCheckInterval = new NumericUpDown();
            label_StatusCheckInterval = new Label();
            numericUpDown_StartBettingSeconds = new NumericUpDown();
            label_StartBettingSeconds = new Label();
            tabPage_Server = new TabPage();
            groupBox_Server = new GroupBox();
            numericUpDown_TimeoutSeconds = new NumericUpDown();
            label_TimeoutSeconds = new Label();
            textBox_ServerUrl = new TextBox();
            label_ServerUrl = new Label();
            tabPage_Behavior = new TabPage();
            groupBox_Behavior = new GroupBox();
            checkBox_ShowConfigOnStartup = new CheckBox();
            numericUpDown_LogLevel = new NumericUpDown();
            label_LogLevel = new Label();
            checkBox_PreventDuplicateBetting = new CheckBox();
            checkBox_RandomizeUserOrder = new CheckBox();
            button_Save = new Button();
            button_Cancel = new Button();
            button_ResetDefault = new Button();
            tabControl_Main.SuspendLayout();
            tabPage_TopUp.SuspendLayout();
            groupBox_TopUp.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_TopUpMax).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_TopUpMin).BeginInit();
            tabPage_Betting.SuspendLayout();
            groupBox_Betting.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_BettingMax).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_BettingMin).BeginInit();
            tabPage_Timing.SuspendLayout();
            groupBox_Timing.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_MaxWaitTime).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_MinWaitTime).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_StatusCheckInterval).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_StartBettingSeconds).BeginInit();
            tabPage_Server.SuspendLayout();
            groupBox_Server.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_TimeoutSeconds).BeginInit();
            tabPage_Behavior.SuspendLayout();
            groupBox_Behavior.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_LogLevel).BeginInit();
            SuspendLayout();
            // 
            // tabControl_Main
            // 
            tabControl_Main.Controls.Add(tabPage_TopUp);
            tabControl_Main.Controls.Add(tabPage_Betting);
            tabControl_Main.Controls.Add(tabPage_Timing);
            tabControl_Main.Controls.Add(tabPage_Server);
            tabControl_Main.Controls.Add(tabPage_Behavior);
            tabControl_Main.Location = new Point(12, 12);
            tabControl_Main.Name = "tabControl_Main";
            tabControl_Main.SelectedIndex = 0;
            tabControl_Main.Size = new Size(560, 380);
            tabControl_Main.TabIndex = 0;
            // 
            // tabPage_TopUp
            // 
            tabPage_TopUp.Controls.Add(groupBox_TopUp);
            tabPage_TopUp.Location = new Point(4, 29);
            tabPage_TopUp.Name = "tabPage_TopUp";
            tabPage_TopUp.Padding = new Padding(3);
            tabPage_TopUp.Size = new Size(552, 347);
            tabPage_TopUp.TabIndex = 0;
            tabPage_TopUp.Text = "上分配置";
            tabPage_TopUp.UseVisualStyleBackColor = true;
            // 
            // groupBox_TopUp
            // 
            groupBox_TopUp.Controls.Add(textBox_TopUpFormat);
            groupBox_TopUp.Controls.Add(label_TopUpFormat);
            groupBox_TopUp.Controls.Add(checkBox_TopUpEnabled);
            groupBox_TopUp.Controls.Add(numericUpDown_TopUpMax);
            groupBox_TopUp.Controls.Add(label_TopUpMax);
            groupBox_TopUp.Controls.Add(numericUpDown_TopUpMin);
            groupBox_TopUp.Controls.Add(label_TopUpMin);
            groupBox_TopUp.Location = new Point(6, 6);
            groupBox_TopUp.Name = "groupBox_TopUp";
            groupBox_TopUp.Size = new Size(540, 335);
            groupBox_TopUp.TabIndex = 0;
            groupBox_TopUp.TabStop = false;
            groupBox_TopUp.Text = "上分设置";
            //
            // textBox_TopUpFormat
            //
            textBox_TopUpFormat.Location = new Point(120, 100);
            textBox_TopUpFormat.Name = "textBox_TopUpFormat";
            textBox_TopUpFormat.Size = new Size(200, 27);
            textBox_TopUpFormat.TabIndex = 8;
            //
            // label_TopUpFormat
            //
            label_TopUpFormat.AutoSize = true;
            label_TopUpFormat.Location = new Point(20, 103);
            label_TopUpFormat.Name = "label_TopUpFormat";
            label_TopUpFormat.Size = new Size(84, 20);
            label_TopUpFormat.TabIndex = 7;
            label_TopUpFormat.Text = "消息格式：";
            //
            // checkBox_TopUpEnabled
            //
            checkBox_TopUpEnabled.AutoSize = true;
            checkBox_TopUpEnabled.Location = new Point(20, 140);
            checkBox_TopUpEnabled.Name = "checkBox_TopUpEnabled";
            checkBox_TopUpEnabled.Size = new Size(106, 24);
            checkBox_TopUpEnabled.TabIndex = 6;
            checkBox_TopUpEnabled.Text = "启用自动上分";
            checkBox_TopUpEnabled.UseVisualStyleBackColor = true;

            // 
            // numericUpDown_TopUpMax
            // 
            numericUpDown_TopUpMax.Location = new Point(120, 60);
            numericUpDown_TopUpMax.Maximum = new decimal(new int[] { 100000, 0, 0, 0 });
            numericUpDown_TopUpMax.Minimum = new decimal(new int[] { 1000, 0, 0, 0 });
            numericUpDown_TopUpMax.Name = "numericUpDown_TopUpMax";
            numericUpDown_TopUpMax.Size = new Size(150, 27);
            numericUpDown_TopUpMax.TabIndex = 3;
            numericUpDown_TopUpMax.Value = new decimal(new int[] { 20000, 0, 0, 0 });
            // 
            // label_TopUpMax
            // 
            label_TopUpMax.AutoSize = true;
            label_TopUpMax.Location = new Point(20, 62);
            label_TopUpMax.Name = "label_TopUpMax";
            label_TopUpMax.Size = new Size(84, 20);
            label_TopUpMax.TabIndex = 2;
            label_TopUpMax.Text = "最大金额：";
            // 
            // numericUpDown_TopUpMin
            // 
            numericUpDown_TopUpMin.Location = new Point(120, 20);
            numericUpDown_TopUpMin.Maximum = new decimal(new int[] { 50000, 0, 0, 0 });
            numericUpDown_TopUpMin.Minimum = new decimal(new int[] { 1000, 0, 0, 0 });
            numericUpDown_TopUpMin.Name = "numericUpDown_TopUpMin";
            numericUpDown_TopUpMin.Size = new Size(150, 27);
            numericUpDown_TopUpMin.TabIndex = 1;
            numericUpDown_TopUpMin.Value = new decimal(new int[] { 5000, 0, 0, 0 });
            // 
            // label_TopUpMin
            // 
            label_TopUpMin.AutoSize = true;
            label_TopUpMin.Location = new Point(20, 22);
            label_TopUpMin.Name = "label_TopUpMin";
            label_TopUpMin.Size = new Size(84, 20);
            label_TopUpMin.TabIndex = 0;
            label_TopUpMin.Text = "最小金额：";
            // 
            // tabPage_Betting
            // 
            tabPage_Betting.Controls.Add(groupBox_Betting);
            tabPage_Betting.Location = new Point(4, 29);
            tabPage_Betting.Name = "tabPage_Betting";
            tabPage_Betting.Padding = new Padding(3);
            tabPage_Betting.Size = new Size(552, 347);
            tabPage_Betting.TabIndex = 1;
            tabPage_Betting.Text = "投注配置";
            tabPage_Betting.UseVisualStyleBackColor = true;
            //
            // groupBox_Betting
            //
            groupBox_Betting.Controls.Add(checkBox_BettingEnabled);
            groupBox_Betting.Controls.Add(numericUpDown_BettingMax);
            groupBox_Betting.Controls.Add(label_BettingMax);
            groupBox_Betting.Controls.Add(numericUpDown_BettingMin);
            groupBox_Betting.Controls.Add(label_BettingMin);
            groupBox_Betting.Location = new Point(6, 6);
            groupBox_Betting.Name = "groupBox_Betting";
            groupBox_Betting.Size = new Size(540, 335);
            groupBox_Betting.TabIndex = 0;
            groupBox_Betting.TabStop = false;
            groupBox_Betting.Text = "投注设置";
            //
            // checkBox_BettingEnabled
            //
            checkBox_BettingEnabled.AutoSize = true;
            checkBox_BettingEnabled.Location = new Point(20, 100);
            checkBox_BettingEnabled.Name = "checkBox_BettingEnabled";
            checkBox_BettingEnabled.Size = new Size(106, 24);
            checkBox_BettingEnabled.TabIndex = 6;
            checkBox_BettingEnabled.Text = "启用自动投注";
            checkBox_BettingEnabled.UseVisualStyleBackColor = true;

            //
            // numericUpDown_BettingMax
            //
            numericUpDown_BettingMax.Location = new Point(120, 60);
            numericUpDown_BettingMax.Maximum = new decimal(new int[] { 10000, 0, 0, 0 });
            numericUpDown_BettingMax.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            numericUpDown_BettingMax.Name = "numericUpDown_BettingMax";
            numericUpDown_BettingMax.Size = new Size(150, 27);
            numericUpDown_BettingMax.TabIndex = 3;
            numericUpDown_BettingMax.Value = new decimal(new int[] { 300, 0, 0, 0 });
            //
            // label_BettingMax
            //
            label_BettingMax.AutoSize = true;
            label_BettingMax.Location = new Point(20, 62);
            label_BettingMax.Name = "label_BettingMax";
            label_BettingMax.Size = new Size(84, 20);
            label_BettingMax.TabIndex = 2;
            label_BettingMax.Text = "最大金额：";
            //
            // numericUpDown_BettingMin
            //
            numericUpDown_BettingMin.Location = new Point(120, 20);
            numericUpDown_BettingMin.Maximum = new decimal(new int[] { 1000, 0, 0, 0 });
            numericUpDown_BettingMin.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            numericUpDown_BettingMin.Name = "numericUpDown_BettingMin";
            numericUpDown_BettingMin.Size = new Size(150, 27);
            numericUpDown_BettingMin.TabIndex = 1;
            numericUpDown_BettingMin.Value = new decimal(new int[] { 5, 0, 0, 0 });
            //
            // label_BettingMin
            //
            label_BettingMin.AutoSize = true;
            label_BettingMin.Location = new Point(20, 22);
            label_BettingMin.Name = "label_BettingMin";
            label_BettingMin.Size = new Size(84, 20);
            label_BettingMin.TabIndex = 0;
            label_BettingMin.Text = "最小金额：";
            //
            // tabPage_Timing
            //
            tabPage_Timing.Controls.Add(groupBox_Timing);
            tabPage_Timing.Location = new Point(4, 29);
            tabPage_Timing.Name = "tabPage_Timing";
            tabPage_Timing.Size = new Size(552, 347);
            tabPage_Timing.TabIndex = 2;
            tabPage_Timing.Text = "时机配置";
            tabPage_Timing.UseVisualStyleBackColor = true;
            //
            // groupBox_Timing
            //
            groupBox_Timing.Controls.Add(numericUpDown_MaxWaitTime);
            groupBox_Timing.Controls.Add(label_MaxWaitTime);
            groupBox_Timing.Controls.Add(numericUpDown_MinWaitTime);
            groupBox_Timing.Controls.Add(label_MinWaitTime);
            groupBox_Timing.Controls.Add(numericUpDown_StatusCheckInterval);
            groupBox_Timing.Controls.Add(label_StatusCheckInterval);
            groupBox_Timing.Controls.Add(numericUpDown_StartBettingSeconds);
            groupBox_Timing.Controls.Add(label_StartBettingSeconds);
            groupBox_Timing.Location = new Point(6, 6);
            groupBox_Timing.Name = "groupBox_Timing";
            groupBox_Timing.Size = new Size(540, 335);
            groupBox_Timing.TabIndex = 0;
            groupBox_Timing.TabStop = false;
            groupBox_Timing.Text = "时机设置";
            //
            // numericUpDown_MaxWaitTime
            //
            numericUpDown_MaxWaitTime.Location = new Point(120, 140);
            numericUpDown_MaxWaitTime.Maximum = new decimal(new int[] { 10000, 0, 0, 0 });
            numericUpDown_MaxWaitTime.Minimum = new decimal(new int[] { 100, 0, 0, 0 });
            numericUpDown_MaxWaitTime.Name = "numericUpDown_MaxWaitTime";
            numericUpDown_MaxWaitTime.Size = new Size(150, 27);
            numericUpDown_MaxWaitTime.TabIndex = 7;
            numericUpDown_MaxWaitTime.Value = new decimal(new int[] { 3000, 0, 0, 0 });
            //
            // label_MaxWaitTime
            //
            label_MaxWaitTime.AutoSize = true;
            label_MaxWaitTime.Location = new Point(20, 142);
            label_MaxWaitTime.Name = "label_MaxWaitTime";
            label_MaxWaitTime.Size = new Size(99, 20);
            label_MaxWaitTime.TabIndex = 6;
            label_MaxWaitTime.Text = "最大等待(ms)：";
            //
            // numericUpDown_MinWaitTime
            //
            numericUpDown_MinWaitTime.Location = new Point(120, 100);
            numericUpDown_MinWaitTime.Maximum = new decimal(new int[] { 5000, 0, 0, 0 });
            numericUpDown_MinWaitTime.Minimum = new decimal(new int[] { 100, 0, 0, 0 });
            numericUpDown_MinWaitTime.Name = "numericUpDown_MinWaitTime";
            numericUpDown_MinWaitTime.Size = new Size(150, 27);
            numericUpDown_MinWaitTime.TabIndex = 5;
            numericUpDown_MinWaitTime.Value = new decimal(new int[] { 1000, 0, 0, 0 });
            //
            // label_MinWaitTime
            //
            label_MinWaitTime.AutoSize = true;
            label_MinWaitTime.Location = new Point(20, 102);
            label_MinWaitTime.Name = "label_MinWaitTime";
            label_MinWaitTime.Size = new Size(99, 20);
            label_MinWaitTime.TabIndex = 4;
            label_MinWaitTime.Text = "最小等待(ms)：";
            //
            // numericUpDown_StatusCheckInterval
            //
            numericUpDown_StatusCheckInterval.Location = new Point(120, 60);
            numericUpDown_StatusCheckInterval.Maximum = new decimal(new int[] { 10000, 0, 0, 0 });
            numericUpDown_StatusCheckInterval.Minimum = new decimal(new int[] { 500, 0, 0, 0 });
            numericUpDown_StatusCheckInterval.Name = "numericUpDown_StatusCheckInterval";
            numericUpDown_StatusCheckInterval.Size = new Size(150, 27);
            numericUpDown_StatusCheckInterval.TabIndex = 3;
            numericUpDown_StatusCheckInterval.Value = new decimal(new int[] { 2000, 0, 0, 0 });
            //
            // label_StatusCheckInterval
            //
            label_StatusCheckInterval.AutoSize = true;
            label_StatusCheckInterval.Location = new Point(20, 62);
            label_StatusCheckInterval.Name = "label_StatusCheckInterval";
            label_StatusCheckInterval.Size = new Size(114, 20);
            label_StatusCheckInterval.TabIndex = 2;
            label_StatusCheckInterval.Text = "检查间隔(ms)：";
            //
            // numericUpDown_StartBettingSeconds
            //
            numericUpDown_StartBettingSeconds.Location = new Point(120, 20);
            numericUpDown_StartBettingSeconds.Maximum = new decimal(new int[] { 300, 0, 0, 0 });
            numericUpDown_StartBettingSeconds.Minimum = new decimal(new int[] { 10, 0, 0, 0 });
            numericUpDown_StartBettingSeconds.Name = "numericUpDown_StartBettingSeconds";
            numericUpDown_StartBettingSeconds.Size = new Size(150, 27);
            numericUpDown_StartBettingSeconds.TabIndex = 1;
            numericUpDown_StartBettingSeconds.Value = new decimal(new int[] { 160, 0, 0, 0 });
            //
            // label_StartBettingSeconds
            //
            label_StartBettingSeconds.AutoSize = true;
            label_StartBettingSeconds.Location = new Point(20, 22);
            label_StartBettingSeconds.Name = "label_StartBettingSeconds";
            label_StartBettingSeconds.Size = new Size(99, 20);
            label_StartBettingSeconds.TabIndex = 0;
            label_StartBettingSeconds.Text = "开始投注(秒)：";
            //
            // tabPage_Server
            //
            tabPage_Server.Controls.Add(groupBox_Server);
            tabPage_Server.Location = new Point(4, 29);
            tabPage_Server.Name = "tabPage_Server";
            tabPage_Server.Size = new Size(552, 347);
            tabPage_Server.TabIndex = 3;
            tabPage_Server.Text = "服务器配置";
            tabPage_Server.UseVisualStyleBackColor = true;
            //
            // groupBox_Server
            //
            groupBox_Server.Controls.Add(numericUpDown_TimeoutSeconds);
            groupBox_Server.Controls.Add(label_TimeoutSeconds);
            groupBox_Server.Controls.Add(textBox_ServerUrl);
            groupBox_Server.Controls.Add(label_ServerUrl);
            groupBox_Server.Location = new Point(6, 6);
            groupBox_Server.Name = "groupBox_Server";
            groupBox_Server.Size = new Size(540, 335);
            groupBox_Server.TabIndex = 0;
            groupBox_Server.TabStop = false;
            groupBox_Server.Text = "服务器设置";
            //
            // numericUpDown_TimeoutSeconds
            //
            numericUpDown_TimeoutSeconds.Location = new Point(120, 60);
            numericUpDown_TimeoutSeconds.Maximum = new decimal(new int[] { 30, 0, 0, 0 });
            numericUpDown_TimeoutSeconds.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            numericUpDown_TimeoutSeconds.Name = "numericUpDown_TimeoutSeconds";
            numericUpDown_TimeoutSeconds.Size = new Size(150, 27);
            numericUpDown_TimeoutSeconds.TabIndex = 3;
            numericUpDown_TimeoutSeconds.Value = new decimal(new int[] { 2, 0, 0, 0 });
            //
            // label_TimeoutSeconds
            //
            label_TimeoutSeconds.AutoSize = true;
            label_TimeoutSeconds.Location = new Point(20, 62);
            label_TimeoutSeconds.Name = "label_TimeoutSeconds";
            label_TimeoutSeconds.Size = new Size(84, 20);
            label_TimeoutSeconds.TabIndex = 2;
            label_TimeoutSeconds.Text = "超时时间(秒)：";
            //
            // textBox_ServerUrl
            //
            textBox_ServerUrl.Location = new Point(120, 20);
            textBox_ServerUrl.Name = "textBox_ServerUrl";
            textBox_ServerUrl.Size = new Size(300, 27);
            textBox_ServerUrl.TabIndex = 1;
            //
            // label_ServerUrl
            //
            label_ServerUrl.AutoSize = true;
            label_ServerUrl.Location = new Point(20, 23);
            label_ServerUrl.Name = "label_ServerUrl";
            label_ServerUrl.Size = new Size(84, 20);
            label_ServerUrl.TabIndex = 0;
            label_ServerUrl.Text = "服务器地址：";
            //
            // tabPage_Behavior
            //
            tabPage_Behavior.Controls.Add(groupBox_Behavior);
            tabPage_Behavior.Location = new Point(4, 29);
            tabPage_Behavior.Name = "tabPage_Behavior";
            tabPage_Behavior.Size = new Size(552, 347);
            tabPage_Behavior.TabIndex = 4;
            tabPage_Behavior.Text = "行为配置";
            tabPage_Behavior.UseVisualStyleBackColor = true;
            //
            // groupBox_Behavior
            //
            groupBox_Behavior.Controls.Add(checkBox_ShowConfigOnStartup);
            groupBox_Behavior.Controls.Add(numericUpDown_LogLevel);
            groupBox_Behavior.Controls.Add(label_LogLevel);
            groupBox_Behavior.Controls.Add(checkBox_PreventDuplicateBetting);
            groupBox_Behavior.Controls.Add(checkBox_RandomizeUserOrder);
            groupBox_Behavior.Location = new Point(6, 6);
            groupBox_Behavior.Name = "groupBox_Behavior";
            groupBox_Behavior.Size = new Size(540, 335);
            groupBox_Behavior.TabIndex = 0;
            groupBox_Behavior.TabStop = false;
            groupBox_Behavior.Text = "行为设置";
            //
            // checkBox_ShowConfigOnStartup
            //
            checkBox_ShowConfigOnStartup.AutoSize = true;
            checkBox_ShowConfigOnStartup.Location = new Point(20, 100);
            checkBox_ShowConfigOnStartup.Name = "checkBox_ShowConfigOnStartup";
            checkBox_ShowConfigOnStartup.Size = new Size(136, 24);
            checkBox_ShowConfigOnStartup.TabIndex = 4;
            checkBox_ShowConfigOnStartup.Text = "启动时显示配置";
            checkBox_ShowConfigOnStartup.UseVisualStyleBackColor = true;
            //
            // numericUpDown_LogLevel
            //
            numericUpDown_LogLevel.Location = new Point(120, 140);
            numericUpDown_LogLevel.Maximum = new decimal(new int[] { 2, 0, 0, 0 });
            numericUpDown_LogLevel.Name = "numericUpDown_LogLevel";
            numericUpDown_LogLevel.Size = new Size(150, 27);
            numericUpDown_LogLevel.TabIndex = 3;
            numericUpDown_LogLevel.Value = new decimal(new int[] { 1, 0, 0, 0 });
            //
            // label_LogLevel
            //
            label_LogLevel.AutoSize = true;
            label_LogLevel.Location = new Point(20, 142);
            label_LogLevel.Name = "label_LogLevel";
            label_LogLevel.Size = new Size(69, 20);
            label_LogLevel.TabIndex = 2;
            label_LogLevel.Text = "日志级别：";
            //
            // checkBox_PreventDuplicateBetting
            //
            checkBox_PreventDuplicateBetting.AutoSize = true;
            checkBox_PreventDuplicateBetting.Location = new Point(20, 60);
            checkBox_PreventDuplicateBetting.Name = "checkBox_PreventDuplicateBetting";
            checkBox_PreventDuplicateBetting.Size = new Size(106, 24);
            checkBox_PreventDuplicateBetting.TabIndex = 1;
            checkBox_PreventDuplicateBetting.Text = "防重复投注";
            checkBox_PreventDuplicateBetting.UseVisualStyleBackColor = true;
            //
            // checkBox_RandomizeUserOrder
            //
            checkBox_RandomizeUserOrder.AutoSize = true;
            checkBox_RandomizeUserOrder.Location = new Point(20, 20);
            checkBox_RandomizeUserOrder.Name = "checkBox_RandomizeUserOrder";
            checkBox_RandomizeUserOrder.Size = new Size(136, 24);
            checkBox_RandomizeUserOrder.TabIndex = 0;
            checkBox_RandomizeUserOrder.Text = "随机用户顺序";
            checkBox_RandomizeUserOrder.UseVisualStyleBackColor = true;
            //
            // button_Save
            //
            button_Save.Location = new Point(316, 410);
            button_Save.Name = "button_Save";
            button_Save.Size = new Size(80, 35);
            button_Save.TabIndex = 1;
            button_Save.Text = "保存";
            button_Save.UseVisualStyleBackColor = true;
            button_Save.Click += button_Save_Click;
            //
            // button_Cancel
            //
            button_Cancel.Location = new Point(410, 410);
            button_Cancel.Name = "button_Cancel";
            button_Cancel.Size = new Size(80, 35);
            button_Cancel.TabIndex = 2;
            button_Cancel.Text = "取消";
            button_Cancel.UseVisualStyleBackColor = true;
            button_Cancel.Click += button_Cancel_Click;
            //
            // button_ResetDefault
            //
            button_ResetDefault.Location = new Point(500, 410);
            button_ResetDefault.Name = "button_ResetDefault";
            button_ResetDefault.Size = new Size(80, 35);
            button_ResetDefault.TabIndex = 3;
            button_ResetDefault.Text = "重置";
            button_ResetDefault.UseVisualStyleBackColor = true;
            button_ResetDefault.Click += button_ResetDefault_Click;
            //
            // ConfigForm
            //
            AutoScaleDimensions = new SizeF(9F, 20F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(584, 461);
            Controls.Add(button_ResetDefault);
            Controls.Add(button_Cancel);
            Controls.Add(button_Save);
            Controls.Add(tabControl_Main);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "ConfigForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "机器人配置";
            FormClosing += ConfigForm_FormClosing;
            Load += ConfigForm_Load;
            tabControl_Main.ResumeLayout(false);
            tabPage_TopUp.ResumeLayout(false);
            groupBox_TopUp.ResumeLayout(false);
            groupBox_TopUp.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_TopUpMax).EndInit();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_TopUpMin).EndInit();
            tabPage_Betting.ResumeLayout(false);
            groupBox_Betting.ResumeLayout(false);
            groupBox_Betting.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_BettingMax).EndInit();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_BettingMin).EndInit();
            tabPage_Timing.ResumeLayout(false);
            groupBox_Timing.ResumeLayout(false);
            groupBox_Timing.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_MaxWaitTime).EndInit();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_MinWaitTime).EndInit();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_StatusCheckInterval).EndInit();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_StartBettingSeconds).EndInit();
            tabPage_Server.ResumeLayout(false);
            groupBox_Server.ResumeLayout(false);
            groupBox_Server.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_TimeoutSeconds).EndInit();
            tabPage_Behavior.ResumeLayout(false);
            groupBox_Behavior.ResumeLayout(false);
            groupBox_Behavior.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDown_LogLevel).EndInit();
            ResumeLayout(false);
        }

        #endregion

        private TabControl tabControl_Main;
        private TabPage tabPage_TopUp;
        private GroupBox groupBox_TopUp;
        private NumericUpDown numericUpDown_TopUpMin;
        private Label label_TopUpMin;
        private NumericUpDown numericUpDown_TopUpMax;
        private Label label_TopUpMax;
        private CheckBox checkBox_TopUpEnabled;
        private TextBox textBox_TopUpFormat;
        private Label label_TopUpFormat;
        private TabPage tabPage_Betting;
        private GroupBox groupBox_Betting;
        private CheckBox checkBox_BettingEnabled;
        private NumericUpDown numericUpDown_BettingMax;
        private Label label_BettingMax;
        private NumericUpDown numericUpDown_BettingMin;
        private Label label_BettingMin;
        private TabPage tabPage_Timing;
        private GroupBox groupBox_Timing;
        private NumericUpDown numericUpDown_MaxWaitTime;
        private Label label_MaxWaitTime;
        private NumericUpDown numericUpDown_MinWaitTime;
        private Label label_MinWaitTime;
        private NumericUpDown numericUpDown_StatusCheckInterval;
        private Label label_StatusCheckInterval;
        private NumericUpDown numericUpDown_StartBettingSeconds;
        private Label label_StartBettingSeconds;
        private TabPage tabPage_Server;
        private GroupBox groupBox_Server;
        private NumericUpDown numericUpDown_TimeoutSeconds;
        private Label label_TimeoutSeconds;
        private TextBox textBox_ServerUrl;
        private Label label_ServerUrl;
        private TabPage tabPage_Behavior;
        private GroupBox groupBox_Behavior;
        private CheckBox checkBox_ShowConfigOnStartup;
        private NumericUpDown numericUpDown_LogLevel;
        private Label label_LogLevel;
        private CheckBox checkBox_PreventDuplicateBetting;
        private CheckBox checkBox_RandomizeUserOrder;
        private Button button_Save;
        private Button button_Cancel;
        private Button button_ResetDefault;
    }
}
