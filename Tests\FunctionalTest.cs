using OneChatGoRobot.Config;
using OneChatGoRobot.Enum;
using OneChatGoRobot.Helper;

namespace OneChatGoRobot.Tests;

/// <summary>
/// 功能测试类
/// 验证改进后的彩种配置功能
/// </summary>
public static class FunctionalTest
{
    /// <summary>
    /// 运行所有功能测试
    /// </summary>
    public static void RunAllTests()
    {
        Console.WriteLine("=== OneChatGoRobot 功能测试开始 ===");
        Console.WriteLine($"测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        Console.WriteLine();

        try
        {
            TestConfigUnification();
            TestLotterySwitch();
            TestDatabaseConnection();
            TestIssueTimeGeneration();
            
            Console.WriteLine("✅ 所有测试通过！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试失败: {ex.Message}");
        }
        
        Console.WriteLine();
        Console.WriteLine("=== 功能测试完成 ===");
    }

    /// <summary>
    /// 测试配置统一性
    /// </summary>
    private static void TestConfigUnification()
    {
        Console.WriteLine("🔍 测试配置统一性...");
        
        // 验证默认配置
        var defaultLottery = AppConfig.Lottery.CurrentLottery;
        Console.WriteLine($"   默认彩种: {defaultLottery}");
        
        // 验证配置获取
        var config = AppConfig.Lottery.GetConfig(defaultLottery);
        Console.WriteLine($"   配置名称: {config.Name}");
        Console.WriteLine($"   每期时长: {config.PeriodDuration}秒");
        
        Console.WriteLine("✅ 配置统一性测试通过");
        Console.WriteLine();
    }

    /// <summary>
    /// 测试彩种切换功能
    /// </summary>
    private static void TestLotterySwitch()
    {
        Console.WriteLine("🔄 测试彩种切换功能...");
        
        var originalLottery = AppConfig.Lottery.CurrentLottery;
        Console.WriteLine($"   原始彩种: {originalLottery}");
        
        // 切换到另一个彩种
        var targetLottery = originalLottery == EnumLottery.台湾宾果 
            ? EnumLottery.一六八飞艇 
            : EnumLottery.台湾宾果;
            
        AppConfig.Lottery.CurrentLottery = targetLottery;
        Console.WriteLine($"   切换到: {AppConfig.Lottery.CurrentLottery}");
        
        // 验证配置变更
        var newConfig = AppConfig.Lottery.GetConfig(AppConfig.Lottery.CurrentLottery);
        Console.WriteLine($"   新配置: {newConfig.Name}");
        
        // 恢复原始配置
        AppConfig.Lottery.CurrentLottery = originalLottery;
        Console.WriteLine($"   恢复到: {AppConfig.Lottery.CurrentLottery}");
        
        Console.WriteLine("✅ 彩种切换功能测试通过");
        Console.WriteLine();
    }

    /// <summary>
    /// 测试数据库连接
    /// </summary>
    private static void TestDatabaseConnection()
    {
        Console.WriteLine("🗄️ 测试数据库连接...");
        
        try
        {
            // 测试数据库连接
            var connection = DbHelper.FSql;
            Console.WriteLine("   数据库连接正常");
            
            // 测试基本查询（不会实际执行，只是验证SQL生成）
            var sql = DbHelper.FSql.Select<Models.User>().ToSql();
            Console.WriteLine($"   SQL生成正常: {sql.Length}字符");
            
            Console.WriteLine("✅ 数据库连接测试通过");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"⚠️ 数据库连接测试警告: {ex.Message}");
        }
        
        Console.WriteLine();
    }

    /// <summary>
    /// 测试期号时间生成逻辑
    /// </summary>
    private static void TestIssueTimeGeneration()
    {
        Console.WriteLine("⏰ 测试期号时间生成逻辑...");
        
        try
        {
            // 测试台湾宾果期号生成逻辑
            AppConfig.Lottery.CurrentLottery = EnumLottery.台湾宾果;
            var config1 = AppConfig.Lottery.GetConfig(AppConfig.Lottery.CurrentLottery);
            Console.WriteLine($"   台湾宾果配置: {config1.Name}, {config1.PeriodDuration}秒/期");
            
            // 测试一六八飞艇期号生成逻辑
            AppConfig.Lottery.CurrentLottery = EnumLottery.一六八飞艇;
            var config2 = AppConfig.Lottery.GetConfig(AppConfig.Lottery.CurrentLottery);
            Console.WriteLine($"   一六八飞艇配置: {config2.Name}, {config2.PeriodDuration}秒/期");
            
            // 恢复默认配置
            AppConfig.Lottery.CurrentLottery = EnumLottery.台湾宾果;
            
            Console.WriteLine("✅ 期号时间生成逻辑测试通过");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 期号时间生成测试失败: {ex.Message}");
            throw;
        }
        
        Console.WriteLine();
    }


}
