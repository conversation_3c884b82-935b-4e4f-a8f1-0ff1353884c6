using OneChatGoRobot.Config;

namespace OneChatGoRobot.Services;

/// <summary>
/// 下注项目类型
/// </summary>
public enum BettingCategory
{
    /// <summary>
    /// 番类
    /// </summary>
    Fan,

    /// <summary>
    /// 正类
    /// </summary>
    Zheng,

    /// <summary>
    /// 单双类
    /// </summary>
    OddEven
}

/// <summary>
/// 下注项目
/// </summary>
public class BettingItem
{
    /// <summary>
    /// 项目名称
    /// </summary>
    public string Name { get; set; } = "";

    /// <summary>
    /// 项目类别
    /// </summary>
    public BettingCategory Category { get; set; }

    /// <summary>
    /// 下注金额
    /// </summary>
    public int Amount { get; set; }

    /// <summary>
    /// 格式化为下注字符串
    /// </summary>
    public override string ToString()
    {
        return $"{Name}/{Amount}";
    }
}

/// <summary>
/// 下注策略接口
/// </summary>
public interface IBettingStrategy
{
    /// <summary>
    /// 生成下注项目
    /// </summary>
    /// <returns>下注项目列表</returns>
    List<BettingItem> GenerateBettingItems();

    /// <summary>
    /// 验证下注项目是否合法
    /// </summary>
    /// <param name="items">下注项目</param>
    /// <returns>是否合法</returns>
    bool ValidateBettingItems(List<BettingItem> items);
}

/// <summary>
/// 智能下注策略实现
/// </summary>
public class SmartBettingStrategy : IBettingStrategy
{
    private readonly Random _random;
    private readonly List<string> _availableAmounts;

    // 定义下注项目组
    private readonly Dictionary<BettingCategory, List<string>> _categoryItems = new()
    {
        { BettingCategory.Fan, new List<string> { "1番", "2番", "3番", "4番" } },
        { BettingCategory.Zheng, new List<string> { "1正", "2正", "3正", "4正" } },
        { BettingCategory.OddEven, new List<string> { "单", "双" } }
    };

    // 定义对立项目组（不能同时下注）
    private readonly List<HashSet<string>> _conflictGroups = new()
    {
        new HashSet<string> { "1正", "3正" },  // 1正和3正是对立的
        new HashSet<string> { "2正", "4正" },  // 2正和4正是对立的
        new HashSet<string> { "单", "双" }     // 单和双是对立的
    };

    public SmartBettingStrategy()
    {
        _random = new Random();
        _availableAmounts = GenerateAmountList();
    }

    public List<BettingItem> GenerateBettingItems()
    {
        var items = new List<BettingItem>();

        // 随机选择下注类别
        var categories = (BettingCategory[])System.Enum.GetValues(typeof(BettingCategory));
        var selectedCategory = categories[_random.Next(categories.Length)];

        // 根据类别生成下注项目
        var item = selectedCategory switch
        {
            BettingCategory.Fan => GenerateFanItem(),
            BettingCategory.Zheng => GenerateZhengItem(),
            BettingCategory.OddEven => GenerateOddEvenItem(),
            _ => throw new ArgumentException($"未知的下注类别: {selectedCategory}")
        };

        items.Add(item);

        // 可以扩展为支持多项下注
        // items.AddRange(GenerateAdditionalItems(selectedCategory));

        return items;
    }

    public bool ValidateBettingItems(List<BettingItem> items)
    {
        if (items == null || items.Count == 0)
            return false;

        // 检查金额是否合法
        foreach (var item in items)
        {
            if (item.Amount < AppConfig.Betting.MinAmount ||
                item.Amount > AppConfig.Betting.MaxAmount)
                return false;
        }

        // 检查是否有冲突的下注项目
        var itemNames = items.Select(i => i.Name).ToHashSet();
        foreach (var conflictGroup in _conflictGroups)
        {
            var conflictCount = itemNames.Intersect(conflictGroup).Count();
            if (conflictCount > 1)
                return false;
        }

        return true;
    }

    private BettingItem GenerateFanItem()
    {
        var fanItems = _categoryItems[BettingCategory.Fan];
        var selectedItem = fanItems[_random.Next(fanItems.Count)];

        return new BettingItem
        {
            Name = selectedItem,
            Category = BettingCategory.Fan,
            Amount = GetRandomAmount()
        };
    }

    private BettingItem GenerateZhengItem()
    {
        var zhengItems = _categoryItems[BettingCategory.Zheng];

        // 智能选择：避免选择对立项目
        // 随机选择是选择1、3正还是2、4正
        var groupIndex = _random.Next(2);
        var selectedItem = groupIndex == 0
            ? (_random.Next(2) == 0 ? "1正" : "3正")  // 选择1正或3正
            : (_random.Next(2) == 0 ? "2正" : "4正"); // 选择2正或4正

        return new BettingItem
        {
            Name = selectedItem,
            Category = BettingCategory.Zheng,
            Amount = GetRandomAmount()
        };
    }

    private BettingItem GenerateOddEvenItem()
    {
        var oddEvenItems = _categoryItems[BettingCategory.OddEven];
        var selectedItem = oddEvenItems[_random.Next(oddEvenItems.Count)];

        return new BettingItem
        {
            Name = selectedItem,
            Category = BettingCategory.OddEven,
            Amount = GetRandomAmount()
        };
    }

    private int GetRandomAmount()
    {
        var amountString = _availableAmounts[_random.Next(_availableAmounts.Count)];
        return int.Parse(amountString);
    }

    private static List<string> GenerateAmountList()
    {
        var amounts = new List<string>();
        for (int i = AppConfig.Betting.MinAmount; i <= AppConfig.Betting.MaxAmount; i += AppConfig.Betting.AmountStep)
        {
            amounts.Add(i.ToString());
        }
        return amounts;
    }
}

/// <summary>
/// 下注策略扩展方法
/// </summary>
public static class BettingStrategyExtensions
{
    /// <summary>
    /// 将下注项目列表格式化为消息内容
    /// </summary>
    /// <param name="items">下注项目列表</param>
    /// <returns>格式化的消息内容</returns>
    public static string FormatToMessage(this List<BettingItem> items)
    {
        return string.Join(" ", items.Select(item => item.ToString()));
    }

    /// <summary>
    /// 计算总下注金额
    /// </summary>
    /// <param name="items">下注项目列表</param>
    /// <returns>总金额</returns>
    public static int GetTotalAmount(this List<BettingItem> items)
    {
        return items.Sum(item => item.Amount);
    }
}
