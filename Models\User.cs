namespace OneChatGoRobot.Models;

/// <summary>
/// 用户信息模型
/// 用于存储和管理机器人模拟的用户信息
///
/// 功能说明：
/// 1. 存储用户的唯一标识和昵称
/// 2. 用于机器人下注时的身份识别
/// 3. 与聊天室服务器的用户系统对接
///
/// 数据来源：
/// - 手动添加：通过UI界面输入UID添加
/// - 服务器同步：从聊天室服务器获取用户详细信息
/// </summary>
public class User
{
    /// <summary>
    /// 用户唯一标识
    /// 这是用户在聊天室系统中的唯一ID，用于身份识别和消息发送
    ///
    /// 特点：
    /// - 由聊天室服务器生成的唯一字符串
    /// - 通常为8位字符组合（如：D57UV04N）
    /// - 用于API调用时的身份验证
    /// - 机器人发送消息时必须提供此ID
    ///
    /// 用途：
    /// - 向服务器发送下注消息
    /// - 获取用户详细信息
    /// - 防止重复下注的用户识别
    /// </summary>
    public string Uid { get; set; } = "";

    /// <summary>
    /// 用户昵称
    /// 用户在聊天室中显示的名称，用于日志记录和界面显示
    ///
    /// 特点：
    /// - 从聊天室服务器自动获取
    /// - 用于日志中的友好显示
    /// - 可能包含中文、英文、数字等字符
    ///
    /// 用途：
    /// - 日志记录中显示操作用户
    /// - UI界面中的用户列表显示
    /// - 调试和监控时的用户识别
    /// </summary>
    public string NickName { get; set; } = "";
}