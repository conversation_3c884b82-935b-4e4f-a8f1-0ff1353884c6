using System.Linq.Expressions;
using FreeSql;
using OneChatGoRobot.Services;

namespace OneChatGoRobot.Repositories;

/// <summary>
/// 通用仓储接口
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public interface IRepository<T> where T : class
{
    /// <summary>
    /// 根据ID获取实体
    /// </summary>
    Task<T?> GetByIdAsync(object id, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有实体
    /// </summary>
    Task<List<T>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据条件查询实体
    /// </summary>
    Task<List<T>> FindAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据条件获取单个实体
    /// </summary>
    Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查实体是否存在
    /// </summary>
    Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);

    /// <summary>
    /// 添加实体
    /// </summary>
    Task<T> AddAsync(T entity, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量添加实体
    /// </summary>
    Task<List<T>> AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新实体
    /// </summary>
    Task<T> UpdateAsync(T entity, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除实体
    /// </summary>
    Task<bool> DeleteAsync(T entity, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据条件删除实体
    /// </summary>
    Task<int> DeleteAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取实体数量
    /// </summary>
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// 基于FreeSql的仓储实现
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public class Repository<T> : IRepository<T> where T : class
{
    protected readonly IFreeSql _freeSql;
    protected readonly ILogger _logger;

    public Repository(IFreeSql freeSql, ILogger logger)
    {
        _freeSql = freeSql;
        _logger = logger;
    }

    public virtual async Task<T?> GetByIdAsync(object id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _freeSql.Select<T>().Where(a => a.Equals(id)).ToOneAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, $"获取实体失败: {typeof(T).Name}, ID: {id}");
            return null;
        }
    }

    public virtual async Task<List<T>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _freeSql.Select<T>().ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, $"获取所有实体失败: {typeof(T).Name}");
            return new List<T>();
        }
    }

    public virtual async Task<List<T>> FindAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _freeSql.Select<T>().Where(predicate).ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, $"查询实体失败: {typeof(T).Name}");
            return new List<T>();
        }
    }

    public virtual async Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _freeSql.Select<T>().Where(predicate).ToOneAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, $"查询单个实体失败: {typeof(T).Name}");
            return null;
        }
    }

    public virtual async Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _freeSql.Select<T>().Where(predicate).AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, $"检查实体存在性失败: {typeof(T).Name}");
            return false;
        }
    }

    public virtual async Task<T> AddAsync(T entity, CancellationToken cancellationToken = default)
    {
        try
        {
            await _freeSql.Insert<T>().AppendData(entity).ExecuteAffrowsAsync(cancellationToken);
            _logger.LogDebug($"添加实体成功: {typeof(T).Name}");
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, $"添加实体失败: {typeof(T).Name}");
            throw;
        }
    }

    public virtual async Task<List<T>> AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        try
        {
            var entityList = entities.ToList();
            await _freeSql.Insert<T>().AppendData(entityList).ExecuteAffrowsAsync(cancellationToken);
            _logger.LogDebug($"批量添加实体成功: {typeof(T).Name}, 数量: {entityList.Count}");
            return entityList;
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, $"批量添加实体失败: {typeof(T).Name}");
            throw;
        }
    }

    public virtual async Task<T> UpdateAsync(T entity, CancellationToken cancellationToken = default)
    {
        try
        {
            await _freeSql.Update<T>().SetSource(entity).ExecuteAffrowsAsync(cancellationToken);
            _logger.LogDebug($"更新实体成功: {typeof(T).Name}");
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, $"更新实体失败: {typeof(T).Name}");
            throw;
        }
    }

    public virtual async Task<bool> DeleteAsync(T entity, CancellationToken cancellationToken = default)
    {
        try
        {
            var affectedRows = await _freeSql.Delete<T>().Where(entity).ExecuteAffrowsAsync(cancellationToken);
            var success = affectedRows > 0;
            _logger.LogDebug($"删除实体{(success ? "成功" : "失败")}: {typeof(T).Name}");
            return success;
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, $"删除实体失败: {typeof(T).Name}");
            return false;
        }
    }

    public virtual async Task<int> DeleteAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            var affectedRows = await _freeSql.Delete<T>().Where(predicate).ExecuteAffrowsAsync(cancellationToken);
            _logger.LogDebug($"批量删除实体成功: {typeof(T).Name}, 影响行数: {affectedRows}");
            return affectedRows;
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, $"批量删除实体失败: {typeof(T).Name}");
            return 0;
        }
    }

    public virtual async Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _freeSql.Select<T>();
            if (predicate != null)
            {
                query = query.Where(predicate);
            }
            return (int)await query.CountAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, $"统计实体数量失败: {typeof(T).Name}");
            return 0;
        }
    }
}
