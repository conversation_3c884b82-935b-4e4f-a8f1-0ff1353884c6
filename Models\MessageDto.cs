﻿namespace OneChatGoRobot.Models;

/// <summary>
/// 消息数据传输对象（DTO - Data Transfer Object）
/// 用于在机器人客户端和聊天室服务器之间传输消息数据
///
/// 主要用途：
/// 1. 封装发送到服务器的消息数据
/// 2. 接收从服务器返回的消息信息
/// 3. 统一消息格式，便于序列化和反序列化
///
/// 使用场景：
/// - 机器人发送下注消息
/// - 机器人发送上分消息
/// - 接收聊天室中的消息
/// - API接口数据交换
/// </summary>
public class MessageDto
{
    /// <summary>
    /// 消息唯一标识
    /// 服务器生成的消息ID，用于消息的唯一识别和追踪
    ///
    /// 特点：
    /// - 由服务器自动生成的长整型数字
    /// - 保证消息的唯一性
    /// - 用于消息查询和管理
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 发送者用户ID
    /// 消息发送者的唯一标识，对应User模型中的Uid
    ///
    /// 用途：
    /// - 标识消息的发送者
    /// - 服务器验证发送权限
    /// - 关联用户信息
    /// </summary>
    public string Uid { get; set; } = "";

    /// <summary>
    /// 消息内容
    /// 实际发送的消息文本内容
    ///
    /// 内容类型：
    /// - 下注消息：如"1番/50 2正/100"
    /// - 上分消息：如"上10000"
    /// - 普通聊天：任意文本内容
    ///
    /// 格式说明：
    /// - 下注格式：项目/金额，多个下注用空格分隔
    /// - 上分格式：上 + 金额数字
    /// </summary>
    public string Content { get; set; } = "";

    /// <summary>
    /// 消息类型
    /// 标识消息的类型，默认为文本消息
    ///
    /// 支持的类型：
    /// - "text"：文本消息（默认）
    /// - 可扩展其他类型如图片、文件等
    /// </summary>
    public string Type { get; set; } = "text";

    /// <summary>
    /// 消息创建时间
    /// 消息在服务器端的创建时间戳
    ///
    /// 用途：
    /// - 消息排序和显示
    /// - 时间统计和分析
    /// - 日志记录
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 发送者昵称
    /// 消息发送者的显示名称，用于界面展示
    ///
    /// 特点：
    /// - 从用户信息中获取
    /// - 用于友好的消息显示
    /// - 便于识别消息来源
    /// </summary>
    public string Nickname { get; set; } = "";
}