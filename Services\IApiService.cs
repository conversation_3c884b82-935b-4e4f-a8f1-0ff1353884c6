using System.Text;
using Newtonsoft.Json;
using OneChatGoRobot.Models;

namespace OneChatGoRobot.Services;

/// <summary>
/// API服务接口
/// 定义与聊天室服务器交互的标准接口
/// </summary>
public interface IApiService
{
    /// <summary>
    /// 根据UID获取用户信息
    /// </summary>
    /// <param name="uid">用户唯一标识</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户信息，如果不存在返回null</returns>
    Task<User?> GetUserByUidAsync(string uid, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送文本消息
    /// </summary>
    /// <param name="uid">发送者UID</param>
    /// <param name="content">消息内容</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否发送成功</returns>
    Task<bool> SendTextMessageAsync(string uid, string content, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量发送消息
    /// </summary>
    /// <param name="messages">消息列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送结果列表</returns>
    Task<List<bool>> SendBatchMessagesAsync(List<(string uid, string content)> messages, CancellationToken cancellationToken = default);
}

/// <summary>
/// API服务实现
/// </summary>
public class ApiService : IApiService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger _logger;

    public ApiService(HttpClient httpClient, ILogger logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<User?> GetUserByUidAsync(string uid, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/users/getUserByUid?uid={uid}", cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"获取用户信息失败: {response.StatusCode}");
                return null;
            }

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            if (string.IsNullOrEmpty(content) || !content.Contains("Uid"))
            {
                return null;
            }

            var jsonData = JsonConvert.DeserializeObject<dynamic>(content);
            return new User
            {
                Uid = uid,
                NickName = jsonData?.NickName ?? ""
            };
        }
        catch (Exception ex)
        {
            _logger.LogError($"获取用户信息异常: {ex.Message}");
            return null;
        }
    }

    public async Task<bool> SendTextMessageAsync(string uid, string content, CancellationToken cancellationToken = default)
    {
        try
        {
            var requestBody = new
            {
                Uid = uid,
                Content = content
            };

            var json = JsonConvert.SerializeObject(requestBody);
            var httpContent = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/api/messages/sendText", httpContent, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInfo($"消息发送成功: {uid} -> {content}");
                return true;
            }
            else
            {
                _logger.LogWarning($"消息发送失败: {response.StatusCode}");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"发送消息异常: {ex.Message}");
            return false;
        }
    }

    public async Task<List<bool>> SendBatchMessagesAsync(List<(string uid, string content)> messages, CancellationToken cancellationToken = default)
    {
        var results = new List<bool>();

        foreach (var (uid, content) in messages)
        {
            var result = await SendTextMessageAsync(uid, content, cancellationToken);
            results.Add(result);

            // 批量发送时添加小延迟，避免服务器压力过大
            await Task.Delay(100, cancellationToken);
        }

        return results;
    }
}
