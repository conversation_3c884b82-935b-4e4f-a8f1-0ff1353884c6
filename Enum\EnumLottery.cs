﻿namespace OneChatGoRobot.Enum;

/// <summary>
/// 彩种枚举
/// 定义机器人系统支持的彩票种类，每种彩票有不同的规则和时间安排
///
/// 用途：
/// 1. 控制期号生成规则
/// 2. 确定开奖时间间隔
/// 3. 设置封盘时间
/// 4. 影响下注策略选择
///
/// 扩展性：
/// - 可以轻松添加新的彩种类型
/// - 每个彩种可以有独立的配置
/// - 支持多彩种并发运行
/// </summary>
public enum EnumLottery
{
    /// <summary>
    /// 台湾宾果彩种
    ///
    /// 特点：
    /// - 期号格式：民国年份 + 6位序号（如：112000001）
    /// - 开奖时间：每天07:00开始，每5分钟一期
    /// - 每日期数：203期
    /// - 封盘时间：开奖前10秒（290秒时封盘）
    /// - 每期时长：5分钟（300秒）
    ///
    /// 下注类型：
    /// - 番类：1番、2番、3番、4番
    /// - 正类：1正、2正、3正、4正
    /// - 单双类：单、双
    /// </summary>
    台湾宾果,

    /// <summary>
    /// 一六八飞艇彩种
    ///
    /// 特点：
    /// - 期号格式：日期 + 3位序号（如：20241201001）
    /// - 开奖时间：每天13:05开始，每5分钟一期
    /// - 每日期数：180期
    /// - 封盘时间：开奖前10秒（230秒时封盘）
    /// - 每期时长：4分钟（240秒）
    ///
    /// 下注类型：
    /// - 与台湾宾果相同的下注选项
    /// - 支持相同的智能下注策略
    /// </summary>
    一六八飞艇
}