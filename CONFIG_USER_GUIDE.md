# OneChatGoRobot 配置系统使用指南

## 🎯 配置系统概述

OneChatGoRobot 现在支持通过配置文件和图形界面来管理所有重要参数，包括上分金额范围、投注金额范围、步长、时机控制等。这使得程序更加灵活和易于定制。

## 📁 配置文件位置

配置文件位于程序根目录：
```
OneChatGoRobot/
├── OneChatGoRobot.exe
├── bot-config.json          ← 配置文件
└── 其他程序文件...
```

## 🎮 使用配置系统

### 方法一：图形界面配置（推荐）

#### 1. 打开配置窗口
- 启动 OneChatGoRobot 程序
- 点击主界面上的 **"配置"** 按钮
- 配置窗口将打开，显示5个配置标签页

#### 2. 配置各项参数

##### 📈 上分配置标签页
- **最小金额**: 自动上分的最小金额（默认：5000元）
- **最大金额**: 自动上分的最大金额（默认：20000元）
- **金额步长**: 上分金额的递增步长（默认：1000元）
- **消息格式**: 上分消息的格式，{0}为金额占位符（默认："上{0}"）
- **启用自动上分**: 是否启用自动上分功能

##### 💰 投注配置标签页
- **最小金额**: 投注的最小金额（默认：5元）
- **最大金额**: 投注的最大金额（默认：300元）
- **金额步长**: 投注金额的递增步长（默认：5元）
- **启用自动投注**: 是否启用自动投注功能

##### ⏰ 时机配置标签页
- **开始投注(秒)**: 距离封盘多少秒开始投注（默认：160秒）
- **检查间隔(ms)**: 期号状态检查间隔（默认：2000毫秒）
- **最小等待(ms)**: 用户操作最小等待时间（默认：1000毫秒）
- **最大等待(ms)**: 用户操作最大等待时间（默认：3000毫秒）

##### 🌐 服务器配置标签页
- **服务器地址**: 聊天室服务器URL（默认：http://127.0.0.1:3001）
- **超时时间(秒)**: API请求超时时间（默认：2秒）

##### ⚙️ 行为配置标签页
- **随机用户顺序**: 是否随机打乱用户投注顺序
- **防重复投注**: 是否防止同一用户在同一期重复投注
- **日志级别**: 日志详细程度（0=基本，1=详细，2=调试）
- **启动时显示配置**: 是否在程序启动时显示配置摘要

#### 3. 保存配置
- 修改完参数后，点击 **"保存"** 按钮
- 系统会验证配置的合理性
- 保存成功后配置立即生效
- 可以点击 **"取消"** 放弃修改
- 可以点击 **"重置"** 恢复默认配置

### 方法二：直接编辑配置文件

#### 1. 打开配置文件
使用文本编辑器打开 `bot-config.json` 文件

#### 2. 修改配置参数
```json
{
  "TopUp": {
    "MinAmount": 5000,        // 最小上分金额
    "MaxAmount": 20000,       // 最大上分金额
    "AmountStep": 1000,       // 上分金额步长
    "Enabled": true,          // 是否启用自动上分
    "MessageFormat": "上{0}"  // 上分消息格式
  },
  "Betting": {
    "MinAmount": 5,           // 最小投注金额
    "MaxAmount": 300,         // 最大投注金额
    "AmountStep": 5,          // 投注金额步长
    "Enabled": true           // 是否启用自动投注
  },
  "Timing": {
    "StartBettingSeconds": 160,    // 开始投注时机（秒）
    "StatusCheckInterval": 2000,   // 状态检查间隔（毫秒）
    "MinWaitTime": 1000,          // 最小等待时间（毫秒）
    "MaxWaitTime": 3000           // 最大等待时间（毫秒）
  },
  "Server": {
    "Url": "http://127.0.0.1:3001",  // 服务器地址
    "TimeoutSeconds": 2               // 超时时间（秒）
  },
  "Behavior": {
    "RandomizeUserOrder": true,         // 随机用户顺序
    "PreventDuplicateBetting": true,    // 防重复投注
    "LogLevel": 1,                      // 日志级别
    "ShowConfigOnStartup": true         // 启动时显示配置
  }
}
```

#### 3. 保存并重启
- 保存配置文件
- 重启程序使配置生效

## 🔧 配置参数详解

### 上分配置详解

#### 金额范围设置
- **用途**: 控制机器人自动上分的金额范围
- **建议**: 
  - 最小金额不要低于1000元
  - 最大金额根据实际需要设置
  - 步长建议设为100的倍数

#### 消息格式
- **格式**: 使用 {0} 作为金额占位符
- **示例**: 
  - "上{0}" → "上5000"
  - "充值{0}元" → "充值5000元"
  - "加{0}" → "加5000"

### 投注配置详解

#### 金额范围设置
- **用途**: 控制机器人投注的金额选项
- **计算**: 程序会生成从最小金额到最大金额，按步长递增的所有选项
- **示例**: 最小5，最大300，步长5 → 生成 5,10,15,20...295,300

#### 投注策略
- **番类**: 1番、2番、3番、4番
- **正类**: 1正、2正、3正、4正
- **单双类**: 单、双

### 时机配置详解

#### 开始投注时机
- **含义**: 距离封盘多少秒开始投注
- **建议**: 
  - 不要设置太早（避免过早暴露）
  - 不要设置太晚（确保有足够时间投注）
  - 推荐范围：120-200秒

#### 等待时间
- **用途**: 模拟人类操作的随机延迟
- **建议**: 
  - 最小等待时间不少于500毫秒
  - 最大等待时间不超过5000毫秒
  - 两者差值至少1000毫秒

### 服务器配置详解

#### 服务器地址
- **格式**: http://IP地址:端口号
- **示例**: 
  - 本地服务器: http://127.0.0.1:3001
  - 远程服务器: http://*************:3001

#### 超时设置
- **建议**: 
  - 本地服务器: 1-2秒
  - 远程服务器: 3-5秒
  - 网络较差: 5-10秒

### 行为配置详解

#### 随机用户顺序
- **启用**: 每次投注时随机打乱用户顺序
- **禁用**: 按固定顺序投注
- **建议**: 启用，增加真实性

#### 防重复投注
- **启用**: 同一用户在同一期只能投注一次
- **禁用**: 允许重复投注
- **建议**: 启用，避免异常行为

## 🚨 注意事项

### 配置验证
- 程序会自动验证配置的合理性
- 无效配置会被自动修正为默认值
- 修改配置后建议测试运行

### 备份配置
- 重要配置建议备份 `bot-config.json` 文件
- 可以为不同环境准备不同的配置文件

### 配置生效
- 通过图形界面修改的配置立即生效
- 直接编辑配置文件需要重启程序

### 常见问题
1. **配置文件损坏**: 删除配置文件，程序会自动创建默认配置
2. **参数无效**: 程序会自动修正为合理值
3. **界面异常**: 重启程序通常可以解决

## 📋 配置模板

### 保守配置（适合测试）
```json
{
  "TopUp": {
    "MinAmount": 3000,
    "MaxAmount": 10000,
    "AmountStep": 500
  },
  "Betting": {
    "MinAmount": 5,
    "MaxAmount": 100,
    "AmountStep": 5
  },
  "Timing": {
    "StartBettingSeconds": 180,
    "MinWaitTime": 2000,
    "MaxWaitTime": 5000
  }
}
```

### 激进配置（适合正式使用）
```json
{
  "TopUp": {
    "MinAmount": 5000,
    "MaxAmount": 30000,
    "AmountStep": 1000
  },
  "Betting": {
    "MinAmount": 10,
    "MaxAmount": 500,
    "AmountStep": 10
  },
  "Timing": {
    "StartBettingSeconds": 120,
    "MinWaitTime": 800,
    "MaxWaitTime": 2000
  }
}
```

## 🎉 总结

配置系统让OneChatGoRobot变得更加灵活和强大：

- **易于使用**: 图形界面配置，操作简单
- **高度可定制**: 所有关键参数都可调整
- **安全可靠**: 自动验证和容错机制
- **立即生效**: 配置修改后无需重启

通过合理配置这些参数，您可以让机器人更好地适应不同的使用环境和需求！🚀
