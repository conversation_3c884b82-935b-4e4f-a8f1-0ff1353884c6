using OneChatGoRobot.Config;

namespace OneChatGoRobot.Forms;

/// <summary>
/// 配置管理窗体
/// 提供用户友好的界面来修改机器人配置参数
/// </summary>
public partial class ConfigForm : Form
{
    /// <summary>
    /// 配置是否已修改
    /// </summary>
    private bool _isModified = false;

    /// <summary>
    /// 构造函数
    /// </summary>
    public ConfigForm()
    {
        InitializeComponent();
        LoadConfigToUI();
    }

    /// <summary>
    /// 将配置加载到UI控件
    /// </summary>
    private void LoadConfigToUI()
    {
        var config = ConfigManager.Current;

        // 上分配置
        numericUpDown_TopUpMin.Value = config.TopUp.MinAmount;
        numericUpDown_TopUpMax.Value = config.TopUp.MaxAmount;
        checkBox_TopUpEnabled.Checked = config.TopUp.Enabled;
        textBox_TopUpFormat.Text = config.TopUp.MessageFormat;

        // 投注配置
        numericUpDown_BettingMin.Value = config.Betting.MinAmount;
        numericUpDown_BettingMax.Value = config.Betting.MaxAmount;
        checkBox_BettingEnabled.Checked = config.Betting.Enabled;

        // 时机配置
        numericUpDown_StartBettingSeconds.Value = config.Timing.StartBettingSeconds;
        numericUpDown_StatusCheckInterval.Value = config.Timing.StatusCheckInterval;
        numericUpDown_MinWaitTime.Value = config.Timing.MinWaitTime;
        numericUpDown_MaxWaitTime.Value = config.Timing.MaxWaitTime;

        // 服务器配置
        textBox_ServerUrl.Text = config.Server.Url;
        numericUpDown_TimeoutSeconds.Value = config.Server.TimeoutSeconds;

        // 行为配置
        checkBox_RandomizeUserOrder.Checked = config.Behavior.RandomizeUserOrder;
        checkBox_PreventDuplicateBetting.Checked = config.Behavior.PreventDuplicateBetting;
        numericUpDown_LogLevel.Value = config.Behavior.LogLevel;
        checkBox_ShowConfigOnStartup.Checked = config.Behavior.ShowConfigOnStartup;

        _isModified = false;
    }

    /// <summary>
    /// 将UI控件的值保存到配置
    /// </summary>
    private void SaveUIToConfig()
    {
        var config = ConfigManager.Current;

        // 上分配置
        config.TopUp.MinAmount = (int)numericUpDown_TopUpMin.Value;
        config.TopUp.MaxAmount = (int)numericUpDown_TopUpMax.Value;
        config.TopUp.Enabled = checkBox_TopUpEnabled.Checked;
        config.TopUp.MessageFormat = textBox_TopUpFormat.Text;

        // 投注配置
        config.Betting.MinAmount = (int)numericUpDown_BettingMin.Value;
        config.Betting.MaxAmount = (int)numericUpDown_BettingMax.Value;
        config.Betting.Enabled = checkBox_BettingEnabled.Checked;

        // 时机配置
        config.Timing.StartBettingSeconds = (int)numericUpDown_StartBettingSeconds.Value;
        config.Timing.StatusCheckInterval = (int)numericUpDown_StatusCheckInterval.Value;
        config.Timing.MinWaitTime = (int)numericUpDown_MinWaitTime.Value;
        config.Timing.MaxWaitTime = (int)numericUpDown_MaxWaitTime.Value;

        // 服务器配置
        config.Server.Url = textBox_ServerUrl.Text;
        config.Server.TimeoutSeconds = (int)numericUpDown_TimeoutSeconds.Value;

        // 行为配置
        config.Behavior.RandomizeUserOrder = checkBox_RandomizeUserOrder.Checked;
        config.Behavior.PreventDuplicateBetting = checkBox_PreventDuplicateBetting.Checked;
        config.Behavior.LogLevel = (int)numericUpDown_LogLevel.Value;
        config.Behavior.ShowConfigOnStartup = checkBox_ShowConfigOnStartup.Checked;
    }

    /// <summary>
    /// 保存按钮点击事件
    /// </summary>
    private async void button_Save_Click(object sender, EventArgs e)
    {
        try
        {
            SaveUIToConfig();
            
            if (await ConfigManager.SaveConfigAsync())
            {
                MessageBox.Show("配置保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                _isModified = false;
                DialogResult = DialogResult.OK;
                Close();
            }
            else
            {
                MessageBox.Show("配置保存失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"保存配置时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 取消按钮点击事件
    /// </summary>
    private void button_Cancel_Click(object sender, EventArgs e)
    {
        if (_isModified)
        {
            var result = MessageBox.Show("配置已修改，是否放弃更改？", "确认", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.No)
                return;
        }

        DialogResult = DialogResult.Cancel;
        Close();
    }

    /// <summary>
    /// 重置为默认值按钮点击事件
    /// </summary>
    private async void button_ResetDefault_Click(object sender, EventArgs e)
    {
        var result = MessageBox.Show("确定要重置为默认配置吗？这将覆盖所有当前设置。", "确认重置", 
            MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

        if (result == DialogResult.Yes)
        {
            await ConfigManager.ResetToDefaultAsync();
            LoadConfigToUI();
            MessageBox.Show("已重置为默认配置！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    /// <summary>
    /// 控件值改变事件处理器
    /// </summary>
    private void OnValueChanged(object sender, EventArgs e)
    {
        _isModified = true;
    }

    /// <summary>
    /// 窗体加载事件
    /// </summary>
    private void ConfigForm_Load(object sender, EventArgs e)
    {
        // 绑定值改变事件
        foreach (Control control in this.Controls)
        {
            BindValueChangedEvents(control);
        }
    }

    /// <summary>
    /// 递归绑定控件的值改变事件
    /// </summary>
    private void BindValueChangedEvents(Control parent)
    {
        foreach (Control control in parent.Controls)
        {
            switch (control)
            {
                case NumericUpDown numericUpDown:
                    numericUpDown.ValueChanged += OnValueChanged;
                    break;
                case TextBox textBox:
                    textBox.TextChanged += OnValueChanged;
                    break;
                case CheckBox checkBox:
                    checkBox.CheckedChanged += OnValueChanged;
                    break;
            }

            // 递归处理子控件
            if (control.HasChildren)
            {
                BindValueChangedEvents(control);
            }
        }
    }

    /// <summary>
    /// 窗体关闭事件
    /// </summary>
    private void ConfigForm_FormClosing(object sender, FormClosingEventArgs e)
    {
        if (_isModified && DialogResult != DialogResult.OK)
        {
            var result = MessageBox.Show("配置已修改，是否放弃更改？", "确认", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.No)
            {
                e.Cancel = true;
            }
        }
    }
}
