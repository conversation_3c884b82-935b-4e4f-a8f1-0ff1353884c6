using OneChatGoRobot.Forms;

namespace OneChatGoRobot;

/// <summary>
/// 程序主入口类
/// 负责应用程序的启动和初始化配置
/// </summary>
static class Program
{
    /// <summary>
    /// 应用程序的主入口点
    /// 初始化Windows Forms应用程序并启动主窗体
    /// </summary>
    [STAThread] // 设置为单线程单元模式，这是Windows Forms应用程序的要求
    static void Main()
    {
        // 初始化应用程序配置
        // 包括高DPI设置、默认字体等Windows Forms相关配置
        // 详细信息参见: https://aka.ms/applicationconfiguration
        ApplicationConfiguration.Initialize();

        // 检查是否已有实例在运行
        if (!SingleInstance.IsContinue())
        {
            MessageBox.Show("程序已经在运行中！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        // 显示彩种选择窗口
        using (var lotterySelectionForm = new LotterySelectionForm())
        {
            var result = lotterySelectionForm.ShowDialog();

            // 如果用户取消选择，则退出程序
            if (result != DialogResult.OK || !lotterySelectionForm.IsConfirmed)
            {
                return;
            }
        }

        // 创建并运行主窗体，开始应用程序的消息循环
        // 这将显示主界面并处理用户交互事件
        Application.Run(new FormMain());
    }
}