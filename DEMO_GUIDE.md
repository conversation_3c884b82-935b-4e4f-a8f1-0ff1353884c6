# OneChatGoRobot 启动时彩种选择功能演示指南

## 🎬 功能演示流程

### 第一步：程序启动
```
用户操作：双击 OneChatGoRobot.exe
系统响应：显示彩种选择对话框
```

**预期界面：**
```
┌──────────────────────────────────────────┐
│  🎯  OneChatGoRobot - 彩种选择           │
├──────────────────────────────────────────┤
│                                          │
│  请选择彩种：                            │
│  ┌─────────────────────────────────────┐  │
│  │ ● 台湾宾果        ○ 一六八飞艇      │  │
│  └─────────────────────────────────────┘  │
│                                          │
│  ┌─────────────────────────────────────┐  │
│  │ 彩种信息：                          │  │
│  │ 名称：台湾宾果                      │  │
│  │ 每期时长：300秒                     │  │
│  │ 每日期数：203期                     │  │
│  │ 开始时间：07:00                     │  │
│  └─────────────────────────────────────┘  │
│                                          │
│                        [确定]  [取消]   │
└──────────────────────────────────────────┘
```

### 第二步：彩种选择
```
用户操作：点击"一六八飞艇"单选按钮
系统响应：更新彩种信息显示
```

**预期界面变化：**
```
┌──────────────────────────────────────────┐
│  🎯  OneChatGoRobot - 彩种选择           │
├──────────────────────────────────────────┤
│                                          │
│  请选择彩种：                            │
│  ┌─────────────────────────────────────┐  │
│  │ ○ 台湾宾果        ● 一六八飞艇      │  │
│  └─────────────────────────────────────┘  │
│                                          │
│  ┌─────────────────────────────────────┐  │
│  │ 彩种信息：                          │  │
│  │ 名称：一六八飞艇                    │  │
│  │ 每期时长：240秒                     │  │
│  │ 每日期数：180期                     │  │
│  │ 开始时间：13:05                     │  │
│  └─────────────────────────────────────┘  │
│                                          │
│                        [确定]  [取消]   │
└──────────────────────────────────────────┘
```

### 第三步：确认选择
```
用户操作：点击"确定"按钮 或 按Enter键
系统响应：关闭选择窗口，启动主程序
```

### 第四步：主程序启动
```
系统响应：显示主窗体界面
日志显示：当前选择的彩种信息
```

**预期主界面：**
```
┌──────────────────────────────────────────┐
│ OneChatGoRobot - 聊天室机器人            │
├──────────────────────────────────────────┤
│ [UID输入框________________] [添加用户]   │
├──────────────────────────────────────────┤
│ ┌──────────────────────────────────────┐ │
│ │ UID    │ 昵称    │ 状态    │ 操作   │ │
│ │────────┼─────────┼─────────┼────────│ │
│ │        │         │         │        │ │
│ │        │         │         │        │ │
│ └──────────────────────────────────────┘ │
├──────────────────────────────────────────┤
│                 [开始]                   │
├──────────────────────────────────────────┤
│ 日志输出区域：                           │
│ [12:34:56] 当前彩种：一六八飞艇          │
│ [12:34:56] 彩种配置：每期240秒，每日180期│
│ [12:34:57] 数据库初始化完成              │
│ [12:34:58] 期号时间生成完成              │
└──────────────────────────────────────────┘
```

## 🎯 测试场景

### 场景1：正常选择流程
1. **启动程序** → 显示彩种选择窗口
2. **选择台湾宾果** → 信息区显示台湾宾果详情
3. **点击确定** → 关闭选择窗口
4. **主程序启动** → 显示"当前彩种：台湾宾果"

### 场景2：切换彩种选择
1. **启动程序** → 显示彩种选择窗口（默认台湾宾果）
2. **选择一六八飞艇** → 信息区更新为一六八飞艇详情
3. **再选择台湾宾果** → 信息区恢复台湾宾果详情
4. **点击确定** → 主程序以台湾宾果启动

### 场景3：取消选择
1. **启动程序** → 显示彩种选择窗口
2. **点击取消** → 程序直接退出
3. **不启动主程序** → 无主界面显示

### 场景4：键盘快捷键
1. **启动程序** → 显示彩种选择窗口
2. **按Tab键** → 在控件间切换焦点
3. **按Enter键** → 等同于点击确定按钮
4. **按ESC键** → 等同于点击取消按钮

## 🔍 验证要点

### 界面验证
- [ ] 彩种选择窗口正确显示
- [ ] 单选按钮功能正常
- [ ] 彩种信息实时更新
- [ ] 按钮响应正确

### 功能验证
- [ ] 默认选择台湾宾果
- [ ] 彩种切换信息更新
- [ ] 确认选择启动主程序
- [ ] 取消选择退出程序

### 数据验证
- [ ] 台湾宾果信息正确（300秒/期，203期/天）
- [ ] 一六八飞艇信息正确（240秒/期，180期/天）
- [ ] 主程序显示正确的彩种信息
- [ ] 配置正确保存和应用

### 交互验证
- [ ] 鼠标点击响应
- [ ] 键盘快捷键有效
- [ ] 窗口关闭行为正确
- [ ] 焦点切换正常

## 🐛 常见问题排查

### 问题1：彩种选择窗口不显示
**可能原因：**
- 程序启动异常
- 窗口被其他程序遮挡
- 显示器分辨率问题

**解决方案：**
- 检查程序是否正常启动
- 使用Alt+Tab切换窗口
- 检查任务栏是否有程序图标

### 问题2：点击确定无响应
**可能原因：**
- 事件处理器未正确绑定
- 程序内部异常

**解决方案：**
- 尝试使用Enter键
- 重新启动程序
- 检查程序日志

### 问题3：彩种信息显示错误
**可能原因：**
- 配置数据错误
- 信息更新逻辑问题

**解决方案：**
- 重新选择彩种
- 重启程序
- 检查配置文件

### 问题4：主程序未启动
**可能原因：**
- 选择窗口未正确关闭
- 主程序启动异常

**解决方案：**
- 确保点击了确定按钮
- 检查是否有错误提示
- 重新启动程序

## 📋 测试检查清单

### 启动测试
- [ ] 程序双击启动正常
- [ ] 彩种选择窗口显示
- [ ] 默认选择台湾宾果
- [ ] 窗口居中显示

### 交互测试
- [ ] 台湾宾果单选按钮可选
- [ ] 一六八飞艇单选按钮可选
- [ ] 彩种信息实时更新
- [ ] 确定按钮可点击
- [ ] 取消按钮可点击

### 键盘测试
- [ ] Tab键切换焦点
- [ ] Enter键确认选择
- [ ] ESC键取消选择
- [ ] 空格键切换单选按钮

### 流程测试
- [ ] 选择台湾宾果→确定→主程序启动
- [ ] 选择一六八飞艇→确定→主程序启动
- [ ] 任意选择→取消→程序退出
- [ ] 关闭窗口→程序退出

### 数据测试
- [ ] 台湾宾果配置信息正确
- [ ] 一六八飞艇配置信息正确
- [ ] 主程序日志显示正确彩种
- [ ] 彩种配置正确应用

## 🎉 演示总结

新的启动时彩种选择功能提供了：

1. **专业的用户体验** - 启动时明确选择，避免误操作
2. **直观的界面设计** - 清晰的选择界面和信息展示
3. **完整的交互支持** - 鼠标和键盘操作全支持
4. **可靠的功能实现** - 完善的异常处理和状态管理

这个改进显著提升了程序的专业性和用户友好性！🚀
