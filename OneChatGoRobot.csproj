﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net8.0-windows</TargetFramework>
        <Nullable>enable</Nullable>
        <UseWindowsForms>true</UseWindowsForms>
        <ImplicitUsings>enable</ImplicitUsings>
        <ApplicationIcon>Typeface.ico</ApplicationIcon>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Costura.Fody" Version="6.0.0">
        <PrivateAssets>all</PrivateAssets>
      </PackageReference>
      <PackageReference Include="FreeSql" Version="3.5.206" />
      <PackageReference Include="FreeSql.Provider.Sqlite" Version="3.5.206" />
      <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
      <PackageReference Include="RestSharp" Version="112.1.0" />
    </ItemGroup>

    <ItemGroup>
      <Reference Include="AiHelper">
        <HintPath>D:\TreeDLL\AiHelper.dll</HintPath>
      </Reference>
      <Reference Include="ControlHelper">
        <HintPath>D:\TreeDLL\ControlHelper.dll</HintPath>
      </Reference>
    </ItemGroup>

</Project>