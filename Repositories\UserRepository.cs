using OneChatGoRobot.Models;
using OneChatGoRobot.Services;

namespace OneChatGoRobot.Repositories;

/// <summary>
/// 用户仓储接口
/// </summary>
public interface IUserRepository : IRepository<User>
{
    /// <summary>
    /// 根据UID获取用户
    /// </summary>
    Task<User?> GetByUidAsync(string uid, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查UID是否存在
    /// </summary>
    Task<bool> UidExistsAsync(string uid, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据昵称模糊查询用户
    /// </summary>
    Task<List<User>> SearchByNickNameAsync(string nickName, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取活跃用户（最近添加的用户）
    /// </summary>
    Task<List<User>> GetActiveUsersAsync(int count = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量检查用户是否存在
    /// </summary>
    Task<Dictionary<string, bool>> BatchCheckExistsAsync(List<string> uids, CancellationToken cancellationToken = default);
}

/// <summary>
/// 用户仓储实现
/// </summary>
public class UserRepository : Repository<User>, IUserRepository
{
    public UserRepository(IFreeSql freeSql, ILogger logger) : base(freeSql, logger)
    {
    }

    public async Task<User?> GetByUidAsync(string uid, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(uid))
        {
            _logger.LogWarning("获取用户时UID为空");
            return null;
        }

        try
        {
            return await _freeSql.Select<User>()
                .Where(u => u.Uid == uid)
                .ToOneAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, $"根据UID获取用户失败: {uid}");
            return null;
        }
    }

    public async Task<bool> UidExistsAsync(string uid, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(uid))
        {
            return false;
        }

        try
        {
            return await _freeSql.Select<User>()
                .Where(u => u.Uid == uid)
                .AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, $"检查UID是否存在失败: {uid}");
            return false;
        }
    }

    public async Task<List<User>> SearchByNickNameAsync(string nickName, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(nickName))
        {
            return new List<User>();
        }

        try
        {
            return await _freeSql.Select<User>()
                .Where(u => u.NickName.Contains(nickName))
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, $"根据昵称搜索用户失败: {nickName}");
            return new List<User>();
        }
    }

    public async Task<List<User>> GetActiveUsersAsync(int count = 10, CancellationToken cancellationToken = default)
    {
        try
        {
            // 注意：User模型中没有创建时间字段，这里按UID排序作为示例
            // 实际使用时可能需要添加CreatedAt字段
            return await _freeSql.Select<User>()
                .OrderByDescending(u => u.Uid)
                .Take(count)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, $"获取活跃用户失败，数量: {count}");
            return new List<User>();
        }
    }

    public async Task<Dictionary<string, bool>> BatchCheckExistsAsync(List<string> uids, CancellationToken cancellationToken = default)
    {
        var result = new Dictionary<string, bool>();

        if (uids == null || uids.Count == 0)
        {
            return result;
        }

        try
        {
            // 过滤掉空的UID
            var validUids = uids.Where(uid => !string.IsNullOrWhiteSpace(uid)).ToList();

            if (validUids.Count == 0)
            {
                return result;
            }

            // 查询存在的UID
            var existingUids = await _freeSql.Select<User>()
                .Where(u => validUids.Contains(u.Uid))
                .ToListAsync(u => u.Uid, cancellationToken);

            var existingUidSet = existingUids.ToHashSet();

            // 构建结果字典
            foreach (var uid in validUids)
            {
                result[uid] = existingUidSet.Contains(uid);
            }

            _logger.LogDebug($"批量检查用户存在性完成，检查数量: {validUids.Count}，存在数量: {existingUids.Count}");
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, $"批量检查用户存在性失败，UID数量: {uids.Count}");

            // 发生异常时，将所有UID标记为不存在
            foreach (var uid in uids.Where(u => !string.IsNullOrWhiteSpace(u)))
            {
                result[uid] = false;
            }
        }

        return result;
    }

    /// <summary>
    /// 添加用户前的验证
    /// </summary>
    public override async Task<User> AddAsync(User entity, CancellationToken cancellationToken = default)
    {
        if (entity == null)
        {
            throw new ArgumentNullException(nameof(entity));
        }

        if (string.IsNullOrWhiteSpace(entity.Uid))
        {
            throw new ArgumentException("用户UID不能为空", nameof(entity));
        }

        // 检查UID是否已存在
        if (await UidExistsAsync(entity.Uid, cancellationToken))
        {
            throw new InvalidOperationException($"UID '{entity.Uid}' 已存在");
        }

        return await base.AddAsync(entity, cancellationToken);
    }

    /// <summary>
    /// 批量添加用户前的验证
    /// </summary>
    public override async Task<List<User>> AddRangeAsync(IEnumerable<User> entities, CancellationToken cancellationToken = default)
    {
        var userList = entities?.ToList() ?? new List<User>();

        if (userList.Count == 0)
        {
            return userList;
        }

        // 验证所有用户的UID
        var invalidUsers = userList.Where(u => string.IsNullOrWhiteSpace(u.Uid)).ToList();
        if (invalidUsers.Any())
        {
            throw new ArgumentException($"存在 {invalidUsers.Count} 个用户的UID为空");
        }

        // 检查重复的UID
        var uids = userList.Select(u => u.Uid).ToList();
        var duplicateUids = uids.GroupBy(uid => uid)
            .Where(g => g.Count() > 1)
            .Select(g => g.Key)
            .ToList();

        if (duplicateUids.Any())
        {
            throw new ArgumentException($"存在重复的UID: {string.Join(", ", duplicateUids)}");
        }

        // 检查数据库中是否已存在
        var existsResult = await BatchCheckExistsAsync(uids, cancellationToken);
        var existingUids = existsResult.Where(kvp => kvp.Value).Select(kvp => kvp.Key).ToList();

        if (existingUids.Any())
        {
            throw new InvalidOperationException($"以下UID已存在: {string.Join(", ", existingUids)}");
        }

        return await base.AddRangeAsync(userList, cancellationToken);
    }
}
