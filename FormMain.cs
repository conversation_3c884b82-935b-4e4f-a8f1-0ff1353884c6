using System.Diagnostics;
using AiHelper;
using Newtonsoft.Json;
using OneChatGoRobot.Config;
using OneChatGoRobot.Enum;
using OneChatGoRobot.Forms;
using OneChatGoRobot.Helper;
using OneChatGoRobot.Models;
using RestSharp;

namespace OneChatGoRobot;

/// <summary>
/// 主窗体类
/// 这是机器人应用程序的主界面和核心控制器
///
/// 主要功能模块：
/// 1. 【用户管理】添加、显示和管理机器人用户
/// 2. 【自动下注】控制机器人的自动下注行为
/// 3. 【日志显示】实时显示机器人操作日志
/// 4. 【服务器通信】与聊天室服务器进行API交互
///
/// 设计特点：
/// - 多线程处理，避免界面卡顿
/// - 智能下注策略，模拟真实用户行为
/// - 完善的异常处理和日志记录
/// - 支持取消操作，可以安全停止
/// </summary>
public partial class FormMain : Form
{
    #region 全局变量属性

    // 服务器URL现在通过ConfigManager.Current.Server.Url配置

    /// <summary>
    /// 期号时间线程取消令牌源
    /// 用于控制期号时间管理线程的生命周期
    ///
    /// 功能：
    /// - 控制IssueTimeHelper中的异步任务
    /// - 支持优雅停止期号时间监控
    /// - 避免程序关闭时线程泄漏
    /// </summary>
    private CancellationTokenSource CtsIssueTime { get; set; } = new();

    /// <summary>
    /// 主工作线程取消令牌源
    /// 用于控制机器人主要工作线程的生命周期
    ///
    /// 功能：
    /// - 控制自动下注循环的停止
    /// - 支持用户手动停止机器人
    /// - 确保线程安全退出
    /// </summary>
    private CancellationTokenSource Cts { get; set; } = new();

    /// <summary>
    /// 每期下注用户记录字典
    /// 记录每个期号中已经下注的用户UID，防止重复下注
    ///
    /// 数据结构：
    /// - 键：期号字符串（如"112000001"）
    /// - 值：该期已下注的用户UID列表
    ///
    /// 用途：
    /// - 避免同一用户在同一期重复下注
    /// - 确保下注行为的合理性
    /// - 提高机器人行为的真实性
    ///
    /// 生命周期：
    /// - 每期开始时自动创建条目
    /// - 用户下注后添加到对应期号列表
    /// - 期号结束后数据保留（用于统计）
    /// </summary>
    private Dictionary<string, List<string>> BetIssueUserDic { get; set; } = new();

    /// <summary>
    /// 下注金额选项列表
    /// 定义机器人可选择的下注金额范围，用于随机下注
    ///
    /// 金额范围：
    /// - 最小金额：5元
    /// - 最大金额：300元
    /// - 递增步长：5元
    /// - 总共60个金额选项
    ///
    /// 用途：
    /// - 随机选择下注金额，增加真实性
    /// - 避免固定金额下注被识别为机器人
    /// - 模拟不同用户的下注习惯
    ///
    /// 初始化：在构造函数中自动填充
    /// </summary>
    private List<string> BetMoneyList { get; set; } = new();

    #endregion

    #region UI

    /// <summary>
    /// 主窗体构造函数
    /// 初始化窗体组件和基础数据
    /// </summary>
    public FormMain()
    {
        // 初始化Windows Forms设计器生成的组件
        InitializeComponent();

        // 初始化下注金额选项列表
        // 从配置文件生成金额选项，支持自定义范围和步长
        BetMoneyList = ConfigManager.GenerateBettingAmounts();
    }

    /// <summary>
    /// 窗体加载事件处理器
    /// 在窗体显示时执行初始化操作
    ///
    /// 初始化流程：
    /// 1. 初始化用户列表显示控件
    /// 2. 从数据库加载已有用户数据
    /// 3. 生成彩种期号时间数据
    /// 4. 启动期号时间监控线程
    /// </summary>
    /// <param name="sender">事件发送者（窗体本身）</param>
    /// <param name="e">事件参数</param>
    private async void FormMain_Load(object sender, EventArgs e)
    {
        try
        {
            // 加载配置文件
            await ConfigManager.LoadConfigAsync();

            // 显示当前选择的彩种信息
            var config = AppConfig.Lottery.GetConfig(AppConfig.Lottery.CurrentLottery);
            richTextBox_Log.AppendText($"[{DateTime.Now:HH:mm:ss}] 当前彩种：{config.Name}" + Environment.NewLine);
            richTextBox_Log.AppendText($"[{DateTime.Now:HH:mm:ss}] 彩种配置：每期{config.PeriodDuration}秒，每日{config.DailyPeriods}期" + Environment.NewLine);

            // 显示机器人配置信息
            if (ConfigManager.Current.Behavior.ShowConfigOnStartup)
            {
                richTextBox_Log.AppendText($"[{DateTime.Now:HH:mm:ss}] {ConfigManager.GetConfigSummary()}" + Environment.NewLine);
            }

            // 初始化用户列表DataGridView控件
            // 使用ControlHelper设置表格的样式和行为
            ControlHelper.MyControl.InitializeDataGridView(dataGridView_Users);

            // 从数据库加载所有用户数据并绑定到表格
            dataGridView_Users.DataSource = await DbHelper.FSql.Select<User>().ToListAsync();
            dataGridView_Users.Refresh(); // 刷新显示

            // 初始化彩种期号时间信息
            // 根据当前选择的彩种生成期号和时间数据
            await IssueTimeHelper.CreateIssueTimeAsync(CtsIssueTime.Token);

            // 启动期号时间处理线程
            // 开始实时监控当前期号和倒计时
            await IssueTimeHelper.PreIssueTimeAsync(CtsIssueTime.Token);
        }
        catch (Exception ex)
        {
            // 记录初始化过程中的异常，但不中断程序运行
            Debug.WriteLine($"窗体加载异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 添加用户按钮点击事件处理器
    /// 根据输入的UID添加新的机器人用户
    ///
    /// 处理流程：
    /// 1. 获取用户输入的UID
    /// 2. 调用服务器API获取用户详细信息
    /// 3. 检查用户是否已存在
    /// 4. 保存用户到本地数据库
    /// 5. 刷新用户列表显示
    ///
    /// 异常处理：
    /// - 网络请求超时（2秒）
    /// - 用户不存在或服务器错误
    /// - 数据库操作异常
    /// </summary>
    /// <param name="sender">事件发送者（添加按钮）</param>
    /// <param name="e">事件参数</param>
    private async void button_AddUser_Click(object sender, EventArgs e)
    {
        try
        {
            // 检查用户输入是否为空
            if (textBox_Uid.Text.Trim().Length == 0)
            {
                MessageBox.Show(@"请输入用户UID！");
                return;
            }

            // 创建用户对象，从输入框获取UID
            User user = new User { Uid = textBox_Uid.Text };
            textBox_Uid.Text = ""; // 清空输入框，准备下次输入


            if (user.Uid.Contains("http://") && user.Uid.Contains("guid"))
            {
                string guid = Ai.GetTextRight(user.Uid, "guid=");
                // 构建API请求URL，获取用户详细信息
                string url = ConfigManager.Current.Server.Url + ConfigManager.Current.Server.GetUserByGuidPath + "?guid=" + guid;
                RestClient client = new RestClient(url);
                RestRequest request = new RestRequest { Method = Method.Get, Timeout = TimeSpan.FromSeconds(ConfigManager.Current.Server.TimeoutSeconds) };
                request.AddHeader("Content-Type", "application/json");

                // 发送HTTP GET请求到服务器
                RestResponse response = await client.ExecuteAsync(request);

                // 解析服务器返回的JSON数据,预期格式：{"Uid":"D57UV04N","Guid":"b1018044-a943-4978-826b-edead33950f2","NickName":"张三","CreatedAt":"2025-05-25T18:42:13+08:00"}
                if (response.Content!.Contains("Uid"))
                {
                    // 使用Newtonsoft.Json解析JSON响应
                    dynamic? jsonData = JsonConvert.DeserializeObject(response.Content);
                    if (jsonData != null)
                    {
                        // 提取用户昵称信息
                        user.Uid = jsonData.Uid;
                        user.NickName = jsonData.NickName;
                    }
                }
            }
            else
            {
                // 构建API请求URL，获取用户详细信息
                string url = ConfigManager.Current.Server.Url + ConfigManager.Current.Server.GetUserByUidPath + "?uid=" + user.Uid;
                RestClient client = new RestClient(url);
                RestRequest request = new RestRequest { Method = Method.Get, Timeout = TimeSpan.FromSeconds(ConfigManager.Current.Server.TimeoutSeconds) };
                request.AddHeader("Content-Type", "application/json");

                // 发送HTTP GET请求到服务器
                RestResponse response = await client.ExecuteAsync(request);

                // 解析服务器返回的JSON数据,预期格式：{"Uid":"D57UV04N","Guid":"b1018044-a943-4978-826b-edead33950f2","NickName":"张三","CreatedAt":"2025-05-25T18:42:13+08:00"}
                if (response.Content!.Contains("Uid"))
                {
                    // 使用Newtonsoft.Json解析JSON响应
                    dynamic? jsonData = JsonConvert.DeserializeObject(response.Content);
                    if (jsonData != null)
                    {
                        // 提取用户昵称信息
                        user.NickName = jsonData.NickName;
                    }
                }
            }


            // 检查用户是否已经存在于本地数据库
            if (await DbHelper.FSql.Select<User>().Where(a => a.Uid == user.Uid).AnyAsync())
            {
                MessageBox.Show(@"用户已存在！");
                return; // 用户已存在，终止添加操作
            }

            // 将新用户插入到数据库
            await DbHelper.FSql.Insert<User>().AppendData(user).ExecuteAffrowsAsync();

            // 刷新用户列表显示
            dataGridView_Users.DataSource = await DbHelper.FSql.Select<User>().ToListAsync();
            dataGridView_Users.Refresh();
        }
        catch (Exception ex)
        {
            // 记录添加用户过程中的异常
            Debug.WriteLine($"添加用户异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 配置按钮点击事件处理器
    /// 打开配置管理窗口
    /// </summary>
    /// <param name="sender">事件发送者（配置按钮）</param>
    /// <param name="e">事件参数</param>
    private void button_Config_Click(object sender, EventArgs e)
    {
        try
        {
            using (var configForm = new ConfigForm())
            {
                var result = configForm.ShowDialog(this);

                if (result == DialogResult.OK)
                {
                    // 配置已保存，重新生成投注金额列表
                    BetMoneyList = ConfigManager.GenerateBettingAmounts();

                    // 在日志中显示配置更新信息
                    richTextBox_Log.AppendText($"[{DateTime.Now:HH:mm:ss}] 配置已更新" + Environment.NewLine);

                    if (ConfigManager.Current.Behavior.ShowConfigOnStartup)
                    {
                        richTextBox_Log.AppendText($"[{DateTime.Now:HH:mm:ss}] {ConfigManager.GetConfigSummary()}" + Environment.NewLine);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"打开配置窗口时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            Debug.WriteLine($"配置窗口异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 删除用户按钮点击事件处理器
    /// 删除选中的用户
    /// </summary>
    /// <param name="sender">事件发送者（删除用户按钮）</param>
    /// <param name="e">事件参数</param>
    private async void button_DeleteUser_Click(object sender, EventArgs e)
    {
        try
        {
            // 检查是否有选中的行
            if (dataGridView_Users.SelectedRows.Count == 0)
            {
                MessageBox.Show("请先选择要删除的用户！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 获取选中的用户信息
            var selectedRow = dataGridView_Users.SelectedRows[0];
            var user = selectedRow.DataBoundItem as User;

            if (user == null)
            {
                MessageBox.Show("获取用户信息失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // 确认删除对话框
            var result = MessageBox.Show(
                $"确定要删除用户 {user.NickName}（UID: {user.Uid}）吗？\n\n此操作不可撤销！",
                "确认删除",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (result != DialogResult.Yes)
            {
                return;
            }

            // 从数据库删除用户
            int affectedRows = await DbHelper.FSql.Delete<User>()
                .Where(u => u.Uid == user.Uid)
                .ExecuteAffrowsAsync();

            if (affectedRows > 0)
            {
                // 删除成功，刷新用户列表
                dataGridView_Users.DataSource = await DbHelper.FSql.Select<User>().ToListAsync();
                dataGridView_Users.Refresh();

                // 显示成功消息并记录日志
                richTextBox_Log.AppendText($"[{DateTime.Now:HH:mm:ss}] 用户删除成功：{user.NickName}（UID: {user.Uid}）" + Environment.NewLine);
                MessageBox.Show($"用户 {user.NickName} 删除成功！", "删除成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("删除用户失败，用户可能已不存在！", "删除失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
        catch (Exception ex)
        {
            // 记录删除用户过程中的异常
            Debug.WriteLine($"删除用户异常: {ex.Message}");
            richTextBox_Log.AppendText($"[{DateTime.Now:HH:mm:ss}] 删除用户失败：{ex.Message}" + Environment.NewLine);
            MessageBox.Show($"删除用户时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 开始按钮点击事件处理器
    /// 启动机器人的自动工作线程
    ///
    /// 功能：
    /// 1. 禁用开始按钮，防止重复启动
    /// 2. 创建后台工作线程执行机器人逻辑
    /// 3. 设置为后台线程，程序退出时自动结束
    /// </summary>
    /// <param name="sender">事件发送者（开始按钮）</param>
    /// <param name="e">事件参数</param>
    private void button_Start_Click(object sender, EventArgs e)
    {
        // 禁用开始按钮，防止用户重复点击启动多个工作线程
        button_Start.Enabled = false;

        // 创建并启动后台工作线程
        // IsBackground = true 确保主程序退出时线程自动结束
        new Thread(DoWork) { IsBackground = true }.Start();
    }

    private void FormMain_FormClosing(object sender, FormClosingEventArgs e)
    {
        Cts.Cancel();
    }



    #endregion

    /// <summary>
    /// 机器人主工作方法
    /// 这是机器人的核心工作逻辑，包含两个主要阶段：
    ///
    /// 第一阶段：初始化上分
    /// - 所有用户进行一次性的上分操作
    /// - 模拟真实用户的充值行为
    ///
    /// 第二阶段：自动下注循环
    /// - 监控期号时间，在合适时机进行下注
    /// - 随机选择下注内容和金额
    /// - 避免重复下注，提高真实性
    ///
    /// 设计特点：
    /// - 异步处理，不阻塞UI线程
    /// - 智能时机控制，只在封盘前160秒内下注
    /// - 随机化策略，模拟真实用户行为
    /// - 完善的异常处理，确保稳定运行
    /// </summary>
    private async void DoWork()
    {
        try
        {
            // ==================== 第一阶段：初始化上分操作 ====================
            {
                // 获取所有已添加的用户列表
                List<User> userList = await DbHelper.FSql.Select<User>().ToListAsync();

                // 为每个用户执行上分操作
                foreach (User user in userList)
                {
                    // 生成随机的上分金额
                    // 根据配置文件生成符合规则的随机金额
                    // 这样可以模拟不同用户的充值习惯
                    Random random = new Random();
                    int amount = ConfigManager.GenerateRandomTopUpAmount();
                    string content = string.Format(ConfigManager.Current.TopUp.MessageFormat, amount); // 上分消息格式

                    // 构建发送消息的HTTP请求
                    RestClient client = new RestClient(ConfigManager.Current.Server.Url + ConfigManager.Current.Server.SendMessagePath);
                    RestRequest request = new RestRequest { Method = Method.Post, Timeout = TimeSpan.FromSeconds(ConfigManager.Current.Server.TimeoutSeconds) };
                    request.AddHeader("Content-Type", "application/json");

                    // 设置请求体，包含用户UID和消息内容
                    request.AddJsonBody(new
                    {
                        user.Uid,
                        Content = content
                    });

                    // 发送上分消息到服务器
                    await client.ExecuteAsync(request);

                    // 在日志中记录上分操作, 调用UI线程更新方法
                    Invoke(() => richTextBox_Log.AppendText($"[{DateTime.Now:HH:mm:ss}] {user.NickName} 发送消息：{content}" + Environment.NewLine));
                }
            }

            // ==================== 第二阶段：自动下注循环 ====================
            while (Cts.IsCancellationRequested == false)
            {
                // 根据配置检查期号状态，避免过于频繁的检查
                await Task.Delay(ConfigManager.Current.Timing.StatusCheckInterval);

                // 智能时机控制：只在配置的封盘前秒数内开始下注
                // 这样可以避免过早下注，模拟真实用户在临近封盘时的下注行为
                if (IssueTimeHelper.CloseTimeDownDic[AppConfig.Lottery.CurrentLottery] > ConfigManager.Current.Timing.StartBettingSeconds)
                {
                    continue; // 时机未到，跳过本次循环
                }

                // 确保当前期号在下注用户记录字典中存在
                // 如果不存在则创建新的空列表，用于记录本期已下注的用户
                BetIssueUserDic.TryAdd(IssueTimeHelper.IssueTimeNowDic[AppConfig.Lottery.CurrentLottery].Issue, new List<string>());

                // 获取用户列表并根据配置决定是否随机打乱顺序
                // 这样可以避免固定的下注顺序，增加真实性
                List<User> userList = await DbHelper.FSql.Select<User>().ToListAsync();
                Random random = new Random();
                if (ConfigManager.Current.Behavior.RandomizeUserOrder)
                {
                    userList = userList.OrderBy(_ => random.Next()).ToList(); // 使用LINQ随机排序
                }

                // 遍历随机排序后的用户列表，依次进行下注
                foreach (User user in userList)
                {
                    // 防重复下注检查：根据配置决定是否允许重复下注
                    if (ConfigManager.Current.Behavior.PreventDuplicateBetting &&
                        BetIssueUserDic[IssueTimeHelper.IssueTimeNowDic[AppConfig.Lottery.CurrentLottery].Issue].Contains(user.Uid))
                    {
                        continue; // 用户已在本期下注，跳过
                    }

                    // 模拟人类操作的随机时间间隔
                    // 根据配置的等待时间范围，避免机器人行为过于规律被识别
                    int waitTime = random.Next(ConfigManager.Current.Timing.MinWaitTime, ConfigManager.Current.Timing.MaxWaitTime + 1);
                    await Task.Delay(waitTime);
                    try
                    {
                        // ==================== 智能下注策略生成 ====================

                        // 初始化下注内容和金额列表
                        List<string> betContentList = new();
                        List<string> betMoneyList = new();

                        // 从配置文件获取彩票下注的三大类别
                        // 每个类别包含不同的下注选项，模拟真实的彩票玩法
                        var categoryGroups = new List<List<string>>
                        {
                            ConfigManager.Current.Betting.Strategy.FanOptions, // 番类：基于号码位置的玩法
                            ConfigManager.Current.Betting.Strategy.ZhengOptions, // 正类：基于号码大小的玩法
                            ConfigManager.Current.Betting.Strategy.OddEvenOptions // 单双类：基于号码奇偶性的玩法
                        };

                        // 对立项目组定义（当前版本未使用，保留用于未来扩展）
                        // 这些组合在真实彩票中是互斥的，不能同时下注
                        // var opposingGroups = new List<HashSet<string>>
                        // {
                        //     new HashSet<string> { "1正", "3正" },  // 1正和3正是对立的
                        //     new HashSet<string> { "2正", "4正" },  // 2正和4正是对立的
                        //     new HashSet<string> { "单", "双" }     // 单和双是对立的
                        // };

                        // 第一步：随机选择一个下注类别
                        // 这样可以确保下注的多样性，避免总是选择同一类型
                        int categoryIndex = random.Next(categoryGroups.Count);
                        var selectedCategory = categoryGroups[categoryIndex];

                        // 第二步：从选中的类别中智能选择具体项目
                        string selectedItem;

                        // 根据不同类别采用不同的选择策略
                        if (categoryIndex == 1) // 正类：需要考虑对立关系
                        {
                            // 正类的智能选择策略：
                            // 1正和3正是一组（小数组），2正和4正是一组（大数组）
                            // 避免选择对立的项目，增加下注的合理性
                            int groupIndex = random.Next(2);
                            if (groupIndex == 0) // 选择小数组：1正或3正
                            {
                                selectedItem = random.Next(2) == 0 ? "1正" : "3正";
                            }
                            else // 选择大数组：2正或4正
                            {
                                selectedItem = random.Next(2) == 0 ? "2正" : "4正";
                            }
                        }
                        else if (categoryIndex == 2) // 单双类：简单的二选一
                        {
                            // 单双类的选择策略：随机选择单或双
                            selectedItem = random.Next(2) == 0 ? "单" : "双";
                        }
                        else // 番类：完全随机选择
                        {
                            // 番类的选择策略：从4个番中随机选择一个
                            selectedItem = selectedCategory[random.Next(selectedCategory.Count)];
                        }

                        // 第三步：将选中的下注项目添加到列表
                        betContentList.Add(selectedItem);

                        // 第四步：随机选择下注金额
                        // 从预定义的金额列表中随机选择，增加金额的多样性
                        betMoneyList.Add(BetMoneyList[random.Next(BetMoneyList.Count)]);

                        // 将下注内容和金额组合成“A/B C/D”的形式
                        string content = string.Join(" ", betContentList.Zip(betMoneyList, (a, b) => $"{a}/{b}"));

                        // ==================== 发送下注消息到服务器 ====================

                        // 构建HTTP POST请求，发送下注消息
                        RestClient client = new RestClient(ConfigManager.Current.Server.Url + ConfigManager.Current.Server.SendMessagePath);
                        RestRequest request = new RestRequest { Method = Method.Post, Timeout = TimeSpan.FromSeconds(ConfigManager.Current.Server.TimeoutSeconds) };
                        request.AddHeader("Content-Type", "application/json");

                        // 设置请求体，包含用户UID和下注内容
                        request.AddJsonBody(new
                        {
                            user.Uid, // 用户唯一标识
                            Content = content // 下注内容（如："1番/50"）
                        });

                        // 发送请求到聊天室服务器
                        await client.ExecuteAsync(request);

                        // 在日志中记录下注操作，便于监控和调试
                        Invoke(() => richTextBox_Log.AppendText($"[{DateTime.Now:HH:mm:ss}] {user.NickName} 发送消息：{content}" + Environment.NewLine));

                        // 备用的UI线程更新方法（当前版本未使用）
                        // 如果需要在其他线程中更新UI，可以使用Invoke方法
                        // Invoke(() => { richTextBox_Log.AppendText($"[{DateTime.Now:HH:mm:ss}] {ConnectionIdUserDic[user.Uid].Nickname} 发送消息：{content}" + Environment.NewLine); });

                        // 记录用户已在本期下注，防止重复下注
                        // 这是防重复机制的关键步骤
                        BetIssueUserDic[IssueTimeHelper.IssueTimeNowDic[AppConfig.Lottery.CurrentLottery].Issue].Add(user.Uid);

                        // 备用的日志记录方法（当前版本未使用）
                        // richTextBox_Log.AppendText($"[{DateTime.Now:HH:mm:ss}] {UserDic[connection.ConnectionId].Nickname}: {content}" + Environment.NewLine);
                    }
                    catch (Exception ex)
                    {
                        // 记录单个用户下注过程中的异常
                        // 不中断整个循环，确保其他用户可以继续下注
                        Debug.WriteLine($"用户 {user.NickName} 下注异常: {ex.Message}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            // 记录整个工作方法的异常
            // 这里捕获的是系统级别的异常，如数据库连接失败等
            Debug.WriteLine($"机器人工作异常: {ex.Message}");
        }
    }
}

