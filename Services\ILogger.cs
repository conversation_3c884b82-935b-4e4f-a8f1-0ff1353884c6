using System.Diagnostics;
using OneChatGoRobot.Config;

namespace OneChatGoRobot.Services;

/// <summary>
/// 日志级别枚举
/// </summary>
public enum LogLevel
{
    Debug,
    Info,
    Warning,
    Error
}

/// <summary>
/// 日志服务接口
/// </summary>
public interface ILogger
{
    /// <summary>
    /// 记录调试信息
    /// </summary>
    void LogDebug(string message);

    /// <summary>
    /// 记录一般信息
    /// </summary>
    void LogInfo(string message);

    /// <summary>
    /// 记录警告信息
    /// </summary>
    void LogWarning(string message);

    /// <summary>
    /// 记录错误信息
    /// </summary>
    void LogError(string message);

    /// <summary>
    /// 记录异常信息
    /// </summary>
    void LogException(Exception ex, string? additionalMessage = null);
}

/// <summary>
/// 日志服务实现
/// 支持多种输出目标：控制台、文件、UI控件
/// </summary>
public class Logger : ILogger
{
    private readonly RichTextBox? _logTextBox;
    private readonly string? _logFilePath;
    private readonly object _lockObject = new();

    public Logger(RichTextBox? logTextBox = null, string? logFilePath = null)
    {
        _logTextBox = logTextBox;
        _logFilePath = logFilePath;
    }

    public void LogDebug(string message)
    {
        if (AppConfig.Logging.EnableVerboseLogging)
        {
            WriteLog(LogLevel.Debug, message);
        }
    }

    public void LogInfo(string message)
    {
        WriteLog(LogLevel.Info, message);
    }

    public void LogWarning(string message)
    {
        WriteLog(LogLevel.Warning, message);
    }

    public void LogError(string message)
    {
        WriteLog(LogLevel.Error, message);
    }

    public void LogException(Exception ex, string? additionalMessage = null)
    {
        var message = additionalMessage != null 
            ? $"{additionalMessage}: {ex.Message}" 
            : ex.Message;
        
        WriteLog(LogLevel.Error, message);
        
        // 详细异常信息只写入调试输出
        Debug.WriteLine($"异常详情: {ex}");
    }

    private void WriteLog(LogLevel level, string message)
    {
        lock (_lockObject)
        {
            var timestamp = DateTime.Now.ToString(AppConfig.Logging.TimeFormat);
            var levelText = GetLevelText(level);
            var formattedMessage = $"[{timestamp}] [{levelText}] {message}";

            // 输出到调试控制台
            Debug.WriteLine(formattedMessage);

            // 输出到UI控件
            if (_logTextBox != null)
            {
                if (_logTextBox.InvokeRequired)
                {
                    _logTextBox.Invoke(() => AppendToTextBox(formattedMessage, level));
                }
                else
                {
                    AppendToTextBox(formattedMessage, level);
                }
            }

            // 输出到文件
            if (!string.IsNullOrEmpty(_logFilePath))
            {
                try
                {
                    File.AppendAllText(_logFilePath, formattedMessage + Environment.NewLine);
                }
                catch
                {
                    // 文件写入失败时不抛出异常，避免影响主程序
                }
            }
        }
    }

    private void AppendToTextBox(string message, LogLevel level)
    {
        if (_logTextBox == null) return;

        // 根据日志级别设置不同颜色
        var color = level switch
        {
            LogLevel.Error => Color.Red,
            LogLevel.Warning => Color.Orange,
            LogLevel.Info => Color.Black,
            LogLevel.Debug => Color.Gray,
            _ => Color.Black
        };

        _logTextBox.SelectionStart = _logTextBox.TextLength;
        _logTextBox.SelectionLength = 0;
        _logTextBox.SelectionColor = color;
        _logTextBox.AppendText(message + Environment.NewLine);
        _logTextBox.SelectionColor = _logTextBox.ForeColor;
        
        // 自动滚动到底部
        _logTextBox.ScrollToCaret();
        
        // 限制日志长度，避免内存占用过大
        if (_logTextBox.Lines.Length > 1000)
        {
            var lines = _logTextBox.Lines.Skip(200).ToArray();
            _logTextBox.Lines = lines;
        }
    }

    private static string GetLevelText(LogLevel level)
    {
        return level switch
        {
            LogLevel.Debug => "调试",
            LogLevel.Info => "信息",
            LogLevel.Warning => "警告",
            LogLevel.Error => "错误",
            _ => "未知"
        };
    }
}
