using OneChatGoRobot.Enum;

namespace OneChatGoRobot.Config;

/// <summary>
/// 应用程序配置类
/// 集中管理所有配置项，提高可维护性和可扩展性
/// </summary>
public static class AppConfig
{
    /// <summary>
    /// 服务器配置
    /// </summary>
    public static class Server
    {
        /// <summary>
        /// 聊天室服务器基础URL
        /// </summary>
        public static string BaseUrl { get; set; } = "http://127.0.0.1:3001";
        
        /// <summary>
        /// API请求超时时间（秒）
        /// </summary>
        public static int TimeoutSeconds { get; set; } = 2;
        
        /// <summary>
        /// 获取用户信息API端点
        /// </summary>
        public static string GetUserEndpoint => "/api/users/getUserByUid";
        
        /// <summary>
        /// 发送消息API端点
        /// </summary>
        public static string SendMessageEndpoint => "/api/messages/sendText";
    }

    /// <summary>
    /// 下注配置
    /// </summary>
    public static class Betting
    {
        /// <summary>
        /// 最小下注金额
        /// </summary>
        public static int MinAmount { get; set; } = 5;
        
        /// <summary>
        /// 最大下注金额
        /// </summary>
        public static int MaxAmount { get; set; } = 300;
        
        /// <summary>
        /// 金额递增步长
        /// </summary>
        public static int AmountStep { get; set; } = 5;
        
        /// <summary>
        /// 开始下注的时机（距离封盘秒数）
        /// </summary>
        public static int StartBettingSeconds { get; set; } = 160;
        
        /// <summary>
        /// 用户操作最小等待时间（毫秒）
        /// </summary>
        public static int MinWaitTime { get; set; } = 1000;
        
        /// <summary>
        /// 用户操作最大等待时间（毫秒）
        /// </summary>
        public static int MaxWaitTime { get; set; } = 3000;
    }

    /// <summary>
    /// 上分配置
    /// </summary>
    public static class TopUp
    {
        /// <summary>
        /// 最小上分金额
        /// </summary>
        public static int MinAmount { get; set; } = 5000;
        
        /// <summary>
        /// 最大上分金额
        /// </summary>
        public static int MaxAmount { get; set; } = 20000;
        
        /// <summary>
        /// 上分金额步长
        /// </summary>
        public static int AmountStep { get; set; } = 1000;
    }

    /// <summary>
    /// 彩种配置
    /// </summary>
    public static class Lottery
    {
        /// <summary>
        /// 当前选择的彩种
        /// </summary>
        public static EnumLottery CurrentLottery { get; set; } = EnumLottery.台湾宾果;
        
        /// <summary>
        /// 获取彩种配置
        /// </summary>
        public static LotteryConfig GetConfig(EnumLottery lottery)
        {
            return lottery switch
            {
                EnumLottery.台湾宾果 => new LotteryConfig
                {
                    Name = "台湾宾果",
                    PeriodDuration = 300, // 5分钟
                    CloseDuration = 290,  // 封盘时间
                    DailyPeriods = 203,
                    StartTime = new TimeSpan(7, 0, 0)
                },
                EnumLottery.一六八飞艇 => new LotteryConfig
                {
                    Name = "一六八飞艇",
                    PeriodDuration = 240, // 4分钟
                    CloseDuration = 230,  // 封盘时间
                    DailyPeriods = 180,
                    StartTime = new TimeSpan(13, 5, 0)
                },
                _ => throw new ArgumentException($"未知的彩种类型: {lottery}")
            };
        }
    }

    /// <summary>
    /// 数据库配置
    /// </summary>
    public static class Database
    {
        /// <summary>
        /// 数据库文件名
        /// </summary>
        public static string FileName { get; set; } = "OneChatGoRobot.db";
        
        /// <summary>
        /// 最大连接池大小
        /// </summary>
        public static int MaxPoolSize { get; set; } = 100;
        
        /// <summary>
        /// 最小连接池大小
        /// </summary>
        public static int MinPoolSize { get; set; } = 5;
    }

    /// <summary>
    /// 日志配置
    /// </summary>
    public static class Logging
    {
        /// <summary>
        /// 是否启用详细日志
        /// </summary>
        public static bool EnableVerboseLogging { get; set; } = true;
        
        /// <summary>
        /// 日志时间格式
        /// </summary>
        public static string TimeFormat { get; set; } = "HH:mm:ss";
    }
}

/// <summary>
/// 彩种配置模型
/// </summary>
public class LotteryConfig
{
    /// <summary>
    /// 彩种名称
    /// </summary>
    public string Name { get; set; } = "";
    
    /// <summary>
    /// 每期持续时间（秒）
    /// </summary>
    public int PeriodDuration { get; set; }
    
    /// <summary>
    /// 封盘时间（秒）
    /// </summary>
    public int CloseDuration { get; set; }
    
    /// <summary>
    /// 每日期数
    /// </summary>
    public int DailyPeriods { get; set; }
    
    /// <summary>
    /// 开始时间
    /// </summary>
    public TimeSpan StartTime { get; set; }
}
