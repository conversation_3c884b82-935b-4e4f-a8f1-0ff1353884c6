{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"OneChatGoRobot/1.0.0": {"dependencies": {"Costura.Fody": "6.0.0", "FreeSql": "3.5.206", "FreeSql.Provider.Sqlite": "3.5.206", "Newtonsoft.Json": "13.0.3", "RestSharp": "112.1.0", "AiHelper": "1.0.0.0", "ControlHelper": "1.0.0.0"}, "runtime": {"OneChatGoRobot.dll": {}}}, "Costura.Fody/6.0.0": {"dependencies": {"Fody": "6.8.2"}}, "Fody/6.8.2": {}, "FreeSql/3.5.206": {"runtime": {"lib/netstandard2.1/FreeSql.dll": {"assemblyVersion": "3.5.206.0", "fileVersion": "3.5.206.0"}}}, "FreeSql.Provider.Sqlite/3.5.206": {"dependencies": {"FreeSql": "3.5.206", "System.Data.SQLite.Core": "1.0.119"}, "runtime": {"lib/netstandard2.0/FreeSql.Provider.Sqlite.dll": {"assemblyVersion": "3.5.206.0", "fileVersion": "3.5.206.0"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "RestSharp/112.1.0": {"runtime": {"lib/net8.0/RestSharp.dll": {"assemblyVersion": "112.1.0.0", "fileVersion": "112.1.0.0"}}}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"runtime": {"lib/netstandard2.1/System.Data.SQLite.dll": {"assemblyVersion": "1.0.119.0", "fileVersion": "1.0.119.0"}}, "runtimeTargets": {"runtimes/linux-x64/native/SQLite.Interop.dll": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/SQLite.Interop.dll": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/SQLite.Interop.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.0.119.0"}, "runtimes/win-x86/native/SQLite.Interop.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.0.119.0"}}}, "System.Data.SQLite.Core/1.0.119": {"dependencies": {"Stub.System.Data.SQLite.Core.NetStandard": "1.0.119"}}, "AiHelper/1.0.0.0": {"runtime": {"AiHelper.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "ControlHelper/1.0.0.0": {"runtime": {"ControlHelper.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"OneChatGoRobot/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Costura.Fody/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Uriu9GJABMivG0wXMJs6NQ7FNE3pylir1gZEBAWDvpii3cnrmxXnOG44MMDuIVOIk/Xhef7WZFsaCNV+py9qA==", "path": "costura.fody/6.0.0", "hashPath": "costura.fody.6.0.0.nupkg.sha512"}, "Fody/6.8.2": {"type": "package", "serviceable": true, "sha512": "sha512-sjGHrtGS1+kcrv99WXCvujOFBTQp4zCH3ZC9wo2LAtVaJkuLpHghQx3y4k1Q8ZKuDAbEw+HE6ZjPUJQK3ejepQ==", "path": "fody/6.8.2", "hashPath": "fody.6.8.2.nupkg.sha512"}, "FreeSql/3.5.206": {"type": "package", "serviceable": true, "sha512": "sha512-f9gfomldWvQm5WRaBciQTDd2+EJNbilTIxWIGCrIDFoTrDjQ5ol52tNpHjJZIXOrzFT/F8qUcLgEr+IwKrtw2w==", "path": "freesql/3.5.206", "hashPath": "freesql.3.5.206.nupkg.sha512"}, "FreeSql.Provider.Sqlite/3.5.206": {"type": "package", "serviceable": true, "sha512": "sha512-lLkMiF/MkVKlrO7uuyoRnTlgVJXOqK52M1Nrv1nL6878qBLquWnVSk1USRCDIz05Xq+wGKQ+8YbQ/7al/lRi1Q==", "path": "freesql.provider.sqlite/3.5.206", "hashPath": "freesql.provider.sqlite.3.5.206.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "RestSharp/112.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-bOUGNZqhhv/QeCXHTKEUil846yBjwWXpzPKjO67kFvaAOoF361z7jLY5BMnuZ6WD2WQRA750sNMcK7MSPCQ5Mg==", "path": "restsharp/112.1.0", "hashPath": "restsharp.112.1.0.nupkg.sha512"}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-dI7ngiCNgdm+n00nQvFTa+LbHvE9MIQXwMSLRzJI/KAJ7G1WmCachsvfE1CD6xvb3OXJvYYEfv3+S/LHyhN0Rg==", "path": "stub.system.data.sqlite.core.netstandard/1.0.119", "hashPath": "stub.system.data.sqlite.core.netstandard.1.0.119.nupkg.sha512"}, "System.Data.SQLite.Core/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-bhQB8HVtRA+OOYw8UTD1F1kU+nGJ0/OZvH1JmlVUI4bGvgVEWeX1NcHjA765NvUoRVuCPlt8PrEpZ1thSsk1jg==", "path": "system.data.sqlite.core/1.0.119", "hashPath": "system.data.sqlite.core.1.0.119.nupkg.sha512"}, "AiHelper/1.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "ControlHelper/1.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}}}