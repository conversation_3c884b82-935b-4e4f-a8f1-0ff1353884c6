using OneChatGoRobot.Config;
using OneChatGoRobot.Helper;
using OneChatGoRobot.Models;
using OneChatGoRobot.Repositories;

namespace OneChatGoRobot.Services;

/// <summary>
/// 机器人服务接口
/// </summary>
public interface IRobotService
{
    /// <summary>
    /// 启动机器人
    /// </summary>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止机器人
    /// </summary>
    Task StopAsync();

    /// <summary>
    /// 获取机器人状态
    /// </summary>
    RobotStatus GetStatus();

    /// <summary>
    /// 添加用户
    /// </summary>
    Task<bool> AddUserAsync(string uid, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量添加用户
    /// </summary>
    Task<int> AddUsersAsync(List<string> uids, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有用户
    /// </summary>
    Task<List<User>> GetUsersAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 机器人状态
/// </summary>
public class RobotStatus
{
    /// <summary>
    /// 是否正在运行
    /// </summary>
    public bool IsRunning { get; set; }

    /// <summary>
    /// 用户数量
    /// </summary>
    public int UserCount { get; set; }

    /// <summary>
    /// 当前期号
    /// </summary>
    public string CurrentIssue { get; set; } = "";

    /// <summary>
    /// 封盘倒计时
    /// </summary>
    public int CloseTimeDown { get; set; }

    /// <summary>
    /// 开奖倒计时
    /// </summary>
    public int OpenTimeDown { get; set; }

    /// <summary>
    /// 今日发送消息数
    /// </summary>
    public int TodayMessageCount { get; set; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime LastActivityTime { get; set; }
}

/// <summary>
/// 机器人服务实现
/// </summary>
public class RobotService : IRobotService
{
    private readonly IUserRepository _userRepository;
    private readonly IApiService _apiService;
    private readonly IBettingStrategy _bettingStrategy;
    private readonly TaskManager _taskManager;
    private readonly ILogger _logger;

    private readonly Dictionary<string, HashSet<string>> _betIssueUsers = new();
    private readonly Random _random = new();
    private int _todayMessageCount = 0;
    private DateTime _lastResetDate = DateTime.Today;

    public RobotService(
        IUserRepository userRepository,
        IApiService apiService,
        IBettingStrategy bettingStrategy,
        TaskManager taskManager,
        ILogger logger)
    {
        _userRepository = userRepository;
        _apiService = apiService;
        _bettingStrategy = bettingStrategy;
        _taskManager = taskManager;
        _logger = logger;

        RegisterTasks();
    }

    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInfo("正在启动机器人服务...");

            // 初始化期号时间
            await IssueTimeHelper.CreateIssueTimeAsync(cancellationToken);

            // 启动期号时间监控任务
            await _taskManager.StartTaskAsync("IssueTimeMonitor");

            // 执行初始上分操作
            await PerformInitialTopUpAsync(cancellationToken);

            // 启动自动下注任务
            await _taskManager.StartTaskAsync("AutoBetting");

            _logger.LogInfo("机器人服务启动成功");
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, "启动机器人服务失败");
            throw;
        }
    }

    public async Task StopAsync()
    {
        try
        {
            _logger.LogInfo("正在停止机器人服务...");
            await _taskManager.StopAllTasksAsync();
            _logger.LogInfo("机器人服务已停止");
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, "停止机器人服务失败");
        }
    }

    public RobotStatus GetStatus()
    {
        ResetDailyCountIfNeeded();

        var isRunning = _taskManager.GetTaskStatus("AutoBetting") == TaskStatus.Running;
        var currentIssue = IssueTimeHelper.IssueTimeNowDic.TryGetValue(AppConfig.Lottery.CurrentLottery, out var issue)
            ? issue.Issue
            : "";

        return new RobotStatus
        {
            IsRunning = isRunning,
            UserCount = _userRepository.CountAsync().Result,
            CurrentIssue = currentIssue,
            CloseTimeDown = IssueTimeHelper.CloseTimeDownDic.TryGetValue(AppConfig.Lottery.CurrentLottery, out var closeTime) ? closeTime : 0,
            OpenTimeDown = IssueTimeHelper.OpenTimeDownDic.TryGetValue(AppConfig.Lottery.CurrentLottery, out var openTime) ? openTime : 0,
            TodayMessageCount = _todayMessageCount,
            LastActivityTime = DateTime.Now
        };
    }

    public async Task<bool> AddUserAsync(string uid, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(uid))
            {
                _logger.LogWarning("尝试添加空的UID");
                return false;
            }

            // 检查用户是否已存在
            if (await _userRepository.UidExistsAsync(uid, cancellationToken))
            {
                _logger.LogWarning($"用户已存在: {uid}");
                return false;
            }

            // 从服务器获取用户信息
            var userInfo = await _apiService.GetUserByUidAsync(uid, cancellationToken);
            if (userInfo == null)
            {
                _logger.LogWarning($"无法从服务器获取用户信息: {uid}");
                return false;
            }

            // 保存到数据库
            await _userRepository.AddAsync(userInfo, cancellationToken);
            _logger.LogInfo($"成功添加用户: {uid} ({userInfo.NickName})");

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, $"添加用户失败: {uid}");
            return false;
        }
    }

    public async Task<int> AddUsersAsync(List<string> uids, CancellationToken cancellationToken = default)
    {
        if (uids == null || uids.Count == 0)
        {
            return 0;
        }

        var successCount = 0;
        var validUids = uids.Where(uid => !string.IsNullOrWhiteSpace(uid)).ToList();

        _logger.LogInfo($"开始批量添加用户，数量: {validUids.Count}");

        foreach (var uid in validUids)
        {
            if (await AddUserAsync(uid, cancellationToken))
            {
                successCount++;
            }

            // 添加小延迟，避免对服务器造成压力
            await Task.Delay(200, cancellationToken);
        }

        _logger.LogInfo($"批量添加用户完成，成功: {successCount}/{validUids.Count}");
        return successCount;
    }

    public async Task<List<User>> GetUsersAsync(CancellationToken cancellationToken = default)
    {
        return await _userRepository.GetAllAsync(cancellationToken);
    }

    private void RegisterTasks()
    {
        // 注册期号时间监控任务
        _taskManager.RegisterTask("IssueTimeMonitor", async (cancellationToken) =>
        {
            await IssueTimeHelper.PreIssueTimeAsync(cancellationToken);
        });

        // 注册自动下注任务
        _taskManager.RegisterTask("AutoBetting", async (cancellationToken) =>
        {
            await AutoBettingLoopAsync(cancellationToken);
        });
    }

    private async Task PerformInitialTopUpAsync(CancellationToken cancellationToken)
    {
        try
        {
            var users = await _userRepository.GetAllAsync(cancellationToken);
            if (users.Count == 0)
            {
                _logger.LogInfo("没有用户，跳过初始上分操作");
                return;
            }

            _logger.LogInfo($"开始为 {users.Count} 个用户执行初始上分操作");

            foreach (var user in users)
            {
                var amount = _random.Next(
                    AppConfig.TopUp.MinAmount / AppConfig.TopUp.AmountStep,
                    AppConfig.TopUp.MaxAmount / AppConfig.TopUp.AmountStep + 1
                ) * AppConfig.TopUp.AmountStep;

                var content = $"上{amount}";
                var success = await _apiService.SendTextMessageAsync(user.Uid, content, cancellationToken);

                if (success)
                {
                    _todayMessageCount++;
                    _logger.LogInfo($"用户 {user.NickName} 上分成功: {content}");
                }
                else
                {
                    _logger.LogWarning($"用户 {user.NickName} 上分失败: {content}");
                }

                // 添加随机延迟
                await Task.Delay(_random.Next(500, 1500), cancellationToken);
            }

            _logger.LogInfo("初始上分操作完成");
        }
        catch (Exception ex)
        {
            _logger.LogException(ex, "执行初始上分操作失败");
        }
    }

    private async Task AutoBettingLoopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInfo("自动下注循环已启动");

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                // 每2秒检查一次
                await Task.Delay(2000, cancellationToken);

                // 检查是否到了下注时机
                if (!ShouldStartBetting())
                {
                    continue;
                }

                // 确保当前期号在下注记录中存在
                var currentIssue = GetCurrentIssue();
                if (string.IsNullOrEmpty(currentIssue))
                {
                    continue;
                }

                _betIssueUsers.TryAdd(currentIssue, new HashSet<string>());

                // 获取用户列表并随机排序
                var users = await _userRepository.GetAllAsync(cancellationToken);
                if (users.Count == 0)
                {
                    continue;
                }

                var shuffledUsers = users.OrderBy(_ => _random.Next()).ToList();

                // 为每个用户执行下注
                foreach (var user in shuffledUsers)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    // 检查用户是否已在本期下注
                    if (_betIssueUsers[currentIssue].Contains(user.Uid))
                    {
                        continue;
                    }

                    // 随机等待，模拟人类操作
                    var waitTime = _random.Next(AppConfig.Betting.MinWaitTime, AppConfig.Betting.MaxWaitTime);
                    await Task.Delay(waitTime, cancellationToken);

                    // 生成下注内容
                    var bettingItems = _bettingStrategy.GenerateBettingItems();
                    if (!_bettingStrategy.ValidateBettingItems(bettingItems))
                    {
                        _logger.LogWarning($"用户 {user.NickName} 生成的下注项目无效");
                        continue;
                    }

                    var content = bettingItems.FormatToMessage();

                    // 发送下注消息
                    var success = await _apiService.SendTextMessageAsync(user.Uid, content, cancellationToken);
                    if (success)
                    {
                        _betIssueUsers[currentIssue].Add(user.Uid);
                        _todayMessageCount++;
                        _logger.LogInfo($"用户 {user.NickName} 下注成功: {content}");
                    }
                    else
                    {
                        _logger.LogWarning($"用户 {user.NickName} 下注失败: {content}");
                    }
                }
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogException(ex, "自动下注循环异常");
                await Task.Delay(5000, cancellationToken); // 异常后等待5秒再继续
            }
        }

        _logger.LogInfo("自动下注循环已停止");
    }

    private bool ShouldStartBetting()
    {
        if (!IssueTimeHelper.CloseTimeDownDic.TryGetValue(AppConfig.Lottery.CurrentLottery, out var closeTimeDown))
        {
            return false;
        }

        return closeTimeDown > 0 && closeTimeDown <= AppConfig.Betting.StartBettingSeconds;
    }

    private string GetCurrentIssue()
    {
        return IssueTimeHelper.IssueTimeNowDic.TryGetValue(AppConfig.Lottery.CurrentLottery, out var issueTime)
            ? issueTime.Issue
            : "";
    }

    private void ResetDailyCountIfNeeded()
    {
        if (DateTime.Today > _lastResetDate)
        {
            _todayMessageCount = 0;
            _lastResetDate = DateTime.Today;
            _logger.LogInfo("每日消息计数已重置");
        }
    }
}
