namespace OneChatGoRobot.Forms
{
    partial class LotterySelectionForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            label_Title = new Label();
            groupBox_LotterySelection = new GroupBox();
            radioButton_Flight168 = new RadioButton();
            radioButton_TaiwanBingo = new RadioButton();
            label_LotteryInfo = new Label();
            button_OK = new Button();
            button_Cancel = new Button();
            pictureBox_Icon = new PictureBox();
            groupBox_LotterySelection.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox_Icon).BeginInit();
            SuspendLayout();
            // 
            // label_Title
            // 
            label_Title.AutoSize = true;
            label_Title.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
            label_Title.ForeColor = Color.DarkBlue;
            label_Title.Location = new Point(80, 20);
            label_Title.Name = "label_Title";
            label_Title.Size = new Size(200, 22);
            label_Title.TabIndex = 0;
            label_Title.Text = "OneChatGoRobot - 彩种选择";
            // 
            // groupBox_LotterySelection
            // 
            groupBox_LotterySelection.Controls.Add(radioButton_Flight168);
            groupBox_LotterySelection.Controls.Add(radioButton_TaiwanBingo);
            groupBox_LotterySelection.Font = new Font("Microsoft YaHei UI", 9F);
            groupBox_LotterySelection.Location = new Point(30, 60);
            groupBox_LotterySelection.Name = "groupBox_LotterySelection";
            groupBox_LotterySelection.Size = new Size(340, 80);
            groupBox_LotterySelection.TabIndex = 1;
            groupBox_LotterySelection.TabStop = false;
            groupBox_LotterySelection.Text = "请选择彩种";
            // 
            // radioButton_Flight168
            // 
            radioButton_Flight168.AutoSize = true;
            radioButton_Flight168.Location = new Point(180, 35);
            radioButton_Flight168.Name = "radioButton_Flight168";
            radioButton_Flight168.Size = new Size(87, 21);
            radioButton_Flight168.TabIndex = 1;
            radioButton_Flight168.Text = "一六八飞艇";
            radioButton_Flight168.UseVisualStyleBackColor = true;
            radioButton_Flight168.CheckedChanged += radioButton_Flight168_CheckedChanged;
            // 
            // radioButton_TaiwanBingo
            // 
            radioButton_TaiwanBingo.AutoSize = true;
            radioButton_TaiwanBingo.Checked = true;
            radioButton_TaiwanBingo.Location = new Point(30, 35);
            radioButton_TaiwanBingo.Name = "radioButton_TaiwanBingo";
            radioButton_TaiwanBingo.Size = new Size(75, 21);
            radioButton_TaiwanBingo.TabIndex = 0;
            radioButton_TaiwanBingo.TabStop = true;
            radioButton_TaiwanBingo.Text = "台湾宾果";
            radioButton_TaiwanBingo.UseVisualStyleBackColor = true;
            radioButton_TaiwanBingo.CheckedChanged += radioButton_TaiwanBingo_CheckedChanged;
            // 
            // label_LotteryInfo
            // 
            label_LotteryInfo.BackColor = Color.LightYellow;
            label_LotteryInfo.BorderStyle = BorderStyle.FixedSingle;
            label_LotteryInfo.Font = new Font("Microsoft YaHei UI", 9F);
            label_LotteryInfo.Location = new Point(30, 160);
            label_LotteryInfo.Name = "label_LotteryInfo";
            label_LotteryInfo.Size = new Size(340, 100);
            label_LotteryInfo.TabIndex = 2;
            label_LotteryInfo.Text = "彩种信息将在这里显示";
            label_LotteryInfo.TextAlign = ContentAlignment.TopLeft;
            // 
            // button_OK
            // 
            button_OK.BackColor = Color.LightGreen;
            button_OK.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
            button_OK.Location = new Point(200, 280);
            button_OK.Name = "button_OK";
            button_OK.Size = new Size(80, 35);
            button_OK.TabIndex = 3;
            button_OK.Text = "确定";
            button_OK.UseVisualStyleBackColor = false;
            button_OK.Click += button_OK_Click;
            // 
            // button_Cancel
            // 
            button_Cancel.BackColor = Color.LightCoral;
            button_Cancel.Font = new Font("Microsoft YaHei UI", 9F);
            button_Cancel.Location = new Point(290, 280);
            button_Cancel.Name = "button_Cancel";
            button_Cancel.Size = new Size(80, 35);
            button_Cancel.TabIndex = 4;
            button_Cancel.Text = "取消";
            button_Cancel.UseVisualStyleBackColor = false;
            button_Cancel.Click += button_Cancel_Click;
            // 
            // pictureBox_Icon
            //
            pictureBox_Icon.BackColor = Color.LightBlue;
            pictureBox_Icon.Location = new Point(30, 15);
            pictureBox_Icon.Name = "pictureBox_Icon";
            pictureBox_Icon.Size = new Size(32, 32);
            pictureBox_Icon.SizeMode = PictureBoxSizeMode.StretchImage;
            pictureBox_Icon.TabIndex = 5;
            pictureBox_Icon.TabStop = false;
            // 
            // LotterySelectionForm
            // 
            AutoScaleDimensions = new SizeF(7F, 17F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.White;
            ClientSize = new Size(400, 340);
            Controls.Add(pictureBox_Icon);
            Controls.Add(button_Cancel);
            Controls.Add(button_OK);
            Controls.Add(label_LotteryInfo);
            Controls.Add(groupBox_LotterySelection);
            Controls.Add(label_Title);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            KeyPreview = true;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "LotterySelectionForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "彩种选择 - OneChatGoRobot";
            Load += LotterySelectionForm_Load;
            KeyDown += LotterySelectionForm_KeyDown;
            groupBox_LotterySelection.ResumeLayout(false);
            groupBox_LotterySelection.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox_Icon).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Label label_Title;
        private GroupBox groupBox_LotterySelection;
        private RadioButton radioButton_Flight168;
        private RadioButton radioButton_TaiwanBingo;
        private Label label_LotteryInfo;
        private Button button_OK;
        private Button button_Cancel;
        private PictureBox pictureBox_Icon;
    }
}
