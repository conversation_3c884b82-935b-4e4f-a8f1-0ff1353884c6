﻿using FreeSql;

namespace OneChatGoRobot.Helper;

/// <summary>
/// 数据库帮助类
/// 提供全局统一的数据库访问接口，使用FreeSql ORM框架
///
/// 主要功能：
/// 1. 管理SQLite数据库连接
/// 2. 提供统一的数据访问入口
/// 3. 自动管理数据库表结构同步
/// 4. 优化数据库连接池性能
///
/// 设计模式：单例模式，确保全局唯一的数据库实例
/// </summary>
public static class DbHelper
{
    /// <summary>
    /// FreeSql数据库访问实例
    /// 使用单例模式，全局共享同一个数据库连接实例
    ///
    /// 配置说明：
    /// - 数据库类型：SQLite
    /// - 数据库文件：OneChatGoRobot.db（位于程序根目录）
    /// - 连接池配置：最大100个连接，最小5个连接
    /// - 自动同步：启用实体结构自动同步到数据库
    ///
    /// 注意事项：
    /// - FreeSql不会扫描程序集，只有在CRUD操作时才会生成对应的表
    /// - 必须定义为单例模式以确保性能和数据一致性
    /// - 连接池可以有效管理数据库连接，提高并发性能
    /// </summary>
    public static IFreeSql FSql { get; } = new FreeSqlBuilder()
        .UseConnectionString(DataType.Sqlite, @"Data Source=OneChatGoRobot.db;Version=3;Pooling=true;Max Pool Size=100;Min Pool Size=5;")
        .UseAutoSyncStructure(true) // 自动同步实体结构到数据库，FreeSql不会扫描程序集，只有CRUD时才会生成表
        .Build(); // 请务必定义成 Singleton 单例模式，确保全局唯一实例
}