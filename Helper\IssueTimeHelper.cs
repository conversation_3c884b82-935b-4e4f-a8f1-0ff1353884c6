﻿using System.Collections.Concurrent;
using System.Diagnostics;
using OneChatGoRobot.Config;
using OneChatGoRobot.Enum;
using OneChatGoRobot.Models;

namespace OneChatGoRobot.Helper;

/// <summary>
/// 彩种期号时间管理帮助类
/// 这是整个机器人系统的核心时间管理模块，负责彩种期号的生成、时间计算和状态管理
///
/// 主要功能模块：
/// 1. 【期号生成】根据不同彩种的规则生成期号和开奖时间，并存储到数据库
/// 2. 【实时监控】循环监控当前时间，确定当前进行的期号和剩余时间
/// 3. 【倒计时管理】实时计算封盘倒计时和开奖倒计时
/// 4. 【状态控制】为机器人下注提供时间判断依据
///
/// 设计特点：
/// - 使用ConcurrentDictionary确保线程安全
/// - 支持多彩种并发处理
/// - 提供精确的时间计算
/// - 异步处理避免阻塞主线程
/// </summary>
public static class IssueTimeHelper
{
    /// <summary>
    /// 当前期号信息字典
    /// 存储不同彩种的当前期号信息，键为彩种类型，值为期号时间对象
    ///
    /// 用途：
    /// - 记录当前正在进行的期号
    /// - 提供期号信息给下注系统
    /// - 支持多彩种同时运行
    ///
    /// 线程安全：使用ConcurrentDictionary确保多线程访问安全
    /// </summary>
    public static ConcurrentDictionary<EnumLottery, IssueTime> IssueTimeNowDic { get; set; } = new();

    /// <summary>
    /// 封盘倒计时字典
    /// 存储不同彩种的封盘倒计时秒数，键为彩种类型，值为倒计时秒数
    ///
    /// 封盘时间规则：
    /// - 台湾宾果：开奖前10秒封盘（290秒时封盘）
    /// - 一六八飞艇：开奖前10秒封盘（230秒时封盘）
    ///
    /// 用途：
    /// - 控制机器人下注时机（通常在封盘前160秒内开始下注）
    /// - 防止在封盘后继续下注
    /// - 提供UI显示倒计时信息
    /// </summary>
    public static ConcurrentDictionary<EnumLottery, int> CloseTimeDownDic { get; set; } = new();

    /// <summary>
    /// 开奖倒计时字典
    /// 存储不同彩种的开奖倒计时秒数，键为彩种类型，值为倒计时秒数
    ///
    /// 开奖时间规则：
    /// - 台湾宾果：每期5分钟（300秒）
    /// - 一六八飞艇：每期4分钟（240秒）
    ///
    /// 用途：
    /// - 确定当前期号是否结束
    /// - 计算下一期开始时间
    /// - 提供开奖时间提醒
    /// </summary>
    public static ConcurrentDictionary<EnumLottery, int> OpenTimeDownDic { get; set; } = new();

    /// <summary>
    /// 生成期号时间信息
    /// 根据选择的彩种类型，生成对应的期号和开奖时间数据，并存储到数据库
    ///
    /// 功能说明：
    /// 1. 清空现有的期号数据，避免重复数据
    /// 2. 根据彩种类型生成期号规则
    /// 3. 计算每期的开奖时间
    /// 4. 批量插入到数据库中
    ///
    /// 支持的彩种：
    /// - 台湾宾果：使用民国年份+序号的期号格式
    /// - 一六八飞艇：使用日期+序号的期号格式
    /// </summary>
    /// <param name="token">取消令牌，用于支持异步操作的取消</param>
    /// <returns>异步任务</returns>
    public static async Task CreateIssueTimeAsync(CancellationToken token)
    {
        // 初始化当前期号信息字典，确保当前彩种有对应的条目
        IssueTimeNowDic.TryAdd(AppConfig.Lottery.CurrentLottery, new IssueTime());

        // 先清空数据表中的所有期号数据，避免重复生成
        // 只删除有期号的记录，保持数据表结构完整
        await DbHelper.FSql.Delete<IssueTime>()
            .Where(a => !string.IsNullOrEmpty(a.Issue))
            .ExecuteAffrowsAsync(token);

        // 根据不同彩种的规则生成期号和开奖时间，并写入数据库
        if (AppConfig.Lottery.CurrentLottery.Equals(EnumLottery.台湾宾果))
        {
            // 台湾宾果期号生成逻辑
            List<IssueTime> dataList = new();

            // 初始化时间变量
            DateTime flagTime = DateTime.Now;  // 当前时间作为起始时间
            DateTime endTime = DateTime.Now;   // 结束时间（当前实现中未使用）
            int issueIndex = 0;                // 期号序号计数器

            // 遍历年份（当前实现只处理当前年份）
            while (flagTime.Year <= endTime.Year)
            {
                // 遍历12个月
                for (int month = 1; month <= 12; month++)
                {
                    // 遍历每月的所有天数
                    for (int dayIndex = 1; dayIndex <= DateTime.DaysInMonth(flagTime.Year, month); dayIndex++)
                    {
                        issueIndex++; // 递增期号序号

                        // 创建当天首期（第1期）
                        // 台湾宾果期号格式：民国年份 + 6位序号（前面补0）
                        // 例如：112000001 表示民国112年第1期
                        IssueTime issueTime = new IssueTime
                        {
                            // 期号计算：民国年份（西元年-1911）+ 补零的序号
                            Issue = flagTime.Year - 1911 + "000000".Substring(0, 6 - issueIndex.ToString().Length) + issueIndex,
                            // 每天首期开奖时间：上午7:00
                            OpenTime = new DateTime(flagTime.Year, month, dayIndex, 7, 0, 0)
                        };

                        // 只保存当前日期前后5天的数据，减少数据库存储量
                        if (issueTime.OpenTime.Date >= DateTime.Now.AddDays(-5).Date &&
                            issueTime.OpenTime.Date <= DateTime.Now.AddDays(5).Date)
                        {
                            dataList.Add(issueTime);
                        }

                        // 生成当天剩余的期号（第2期到第203期）
                        // 台湾宾果每天共203期，每5分钟一期
                        for (int m = 2; m <= 203; m++)
                        {
                            issueIndex++; // 递增期号序号

                            // 创建后续期号
                            issueTime = new IssueTime
                            {
                                // 继续使用相同的期号格式
                                Issue = flagTime.Year - 1911 + "000000".Substring(0, 6 - issueIndex.ToString().Length) + issueIndex,
                                // 每期间隔5分钟
                                OpenTime = issueTime.OpenTime.AddMinutes(5)
                            };

                            // 同样只保存当前日期前后5天的数据
                            if (issueTime.OpenTime.Date >= DateTime.Now.AddDays(-5).Date &&
                                issueTime.OpenTime.Date <= DateTime.Now.AddDays(5).Date)
                            {
                                dataList.Add(issueTime);
                            }
                        }
                    }
                }

                // 处理下一年（当前实现中实际只处理当前年份）
                flagTime = flagTime.AddYears(1);
            }

            // 批量插入生成的期号数据到数据库
            await DbHelper.FSql.Insert(dataList).ExecuteAffrowsAsync(token);
        }
        else if (AppConfig.Lottery.CurrentLottery.Equals(EnumLottery.一六八飞艇))
        {
            // 一六八飞艇期号生成逻辑
            List<IssueTime> dataList = new();

            // 设置起始时间为当年1月1日
            DateTime flagTime = new DateTime(DateTime.Now.Year, 1, 1);

            // 计算当年总天数（考虑闰年）
            int dayCount = DateTime.Now.Year % 4 == 0 ? 366 : 365;

            // 遍历一年中的每一天
            for (int dayIndex = 1; dayIndex <= dayCount; dayIndex++)
            {
                // 创建当天首期（第001期）
                // 一六八飞艇期号格式：日期(yyyyMMdd) + 3位序号
                // 例如：20241201001 表示2024年12月1日第1期
                IssueTime issueTime = new IssueTime
                {
                    // 期号格式：年月日 + 001
                    Issue = flagTime.ToString("yyyyMMdd") + "001",
                    // 每天首期开奖时间：下午13:05
                    OpenTime = new DateTime(flagTime.Year, flagTime.Month, flagTime.Day, 13, 5, 0)
                };
                dataList.Add(issueTime);

                // 生成当天剩余的期号（第2期到第180期）
                // 一六八飞艇每天共180期，每5分钟一期
                for (int j = 2; j <= 180; j++)
                {
                    // 创建后续期号
                    issueTime = new IssueTime
                    {
                        // 期号递增：将前一期号转换为数字后加1
                        Issue = (Convert.ToInt64(issueTime.Issue) + 1).ToString(),
                        // 每期间隔5分钟
                        OpenTime = issueTime.OpenTime.AddMinutes(5.0)
                    };
                    dataList.Add(issueTime);
                }

                // 处理下一天
                flagTime = flagTime.AddDays(1);
            }

            // 批量插入生成的期号数据到数据库
            await DbHelper.FSql.Insert(dataList).ExecuteAffrowsAsync(token);
        }
    }

    /// <summary>
    /// 实时期号时间处理循环
    /// 这是系统的核心监控线程，负责实时跟踪当前期号状态和时间倒计时
    ///
    /// 主要功能：
    /// 1. 根据当前时间确定正在进行的期号
    /// 2. 实时计算封盘倒计时和开奖倒计时
    /// 3. 为机器人下注系统提供时间判断依据
    /// 4. 处理期号切换逻辑
    ///
    /// 运行机制：
    /// - 每秒执行一次检查，保证时间精度
    /// - 支持取消令牌，可以优雅停止
    /// - 异常处理确保线程稳定运行
    /// </summary>
    /// <param name="token">取消令牌，用于停止循环处理</param>
    /// <returns>异步任务</returns>
    public static async Task PreIssueTimeAsync(CancellationToken token)
    {
        // 初始化倒计时字典，确保当前彩种有对应的条目
        CloseTimeDownDic.TryAdd(AppConfig.Lottery.CurrentLottery, 0);
        OpenTimeDownDic.TryAdd(AppConfig.Lottery.CurrentLottery, 0);

        // 进入主循环，持续监控期号时间状态
        while (!token.IsCancellationRequested)
        {
            try
            {
                // 每秒执行一次检查，避免过度消耗CPU资源
                await Task.Delay(1000, token);

                // 当前期号结束时，需要确定下一个期号
                // 判断条件：封盘倒计时和开奖倒计时都小于等于0
                if (CloseTimeDownDic[AppConfig.Lottery.CurrentLottery] <= 0 && OpenTimeDownDic[AppConfig.Lottery.CurrentLottery] <= 0)
                {
                    // 获取当前时间
                    DateTime now = DateTime.Now;

                    // 根据彩种确定每期持续时间
                    int addSeconds = AppConfig.Lottery.CurrentLottery.Equals(EnumLottery.台湾宾果) ? 300 : 240;

                    // 查找当前正在进行的期号
                    // 优先查找：当前时间在期号开始时间和结束时间之间的期号
                    // 备选查找：如果没有正在进行的期号，则取下一个即将开始的期号
                    IssueTimeNowDic[AppConfig.Lottery.CurrentLottery] =
                        await DbHelper.FSql.Select<IssueTime>()
                            .Where(a => now >= a.OpenTime && now <= a.OpenTime.AddSeconds(addSeconds))
                            .ToOneAsync(token) ??
                        (await DbHelper.FSql.Select<IssueTime>()
                            .Where(a => now <= a.OpenTime)
                            .ToListAsync(token)).First();
                }

                // 根据不同彩种计算倒计时
                switch (AppConfig.Lottery.CurrentLottery)
                {
                    case EnumLottery.台湾宾果:
                        // 台湾宾果时间规则：
                        // - 每期5分钟（300秒）
                        // - 开奖前10秒封盘（290秒时封盘）
                        CloseTimeDownDic[AppConfig.Lottery.CurrentLottery] = (int)IssueTimeNowDic[EnumLottery.台湾宾果].OpenTime.AddSeconds(290.0).Subtract(DateTime.Now).TotalSeconds;
                        OpenTimeDownDic[AppConfig.Lottery.CurrentLottery] = (int)IssueTimeNowDic[EnumLottery.台湾宾果].OpenTime.AddSeconds(300.0).Subtract(DateTime.Now).TotalSeconds;
                        break;

                    case EnumLottery.一六八飞艇:
                        // 一六八飞艇时间规则：
                        // - 每期4分钟（240秒）
                        // - 开奖前10秒封盘（230秒时封盘）
                        CloseTimeDownDic[AppConfig.Lottery.CurrentLottery] = (int)IssueTimeNowDic[EnumLottery.一六八飞艇].OpenTime.AddSeconds(230.0).Subtract(DateTime.Now).TotalSeconds;
                        OpenTimeDownDic[AppConfig.Lottery.CurrentLottery] = (int)IssueTimeNowDic[EnumLottery.一六八飞艇].OpenTime.AddSeconds(240.0).Subtract(DateTime.Now).TotalSeconds;
                        break;
                }
            }
            catch (Exception ex)
            {
                // 记录异常信息，但不中断循环，确保系统稳定运行
                Debug.WriteLine($"期号时间处理异常: {ex.Message}");
            }
        }
    }
}