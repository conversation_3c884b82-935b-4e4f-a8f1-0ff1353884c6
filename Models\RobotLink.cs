﻿using FreeSql.DataAnnotations;

namespace OneChatGoRobot.Models;

/// <summary>
/// 机器人链接模型
/// 用于存储机器人连接的聊天室链接信息
/// </summary>
[Table(Name = "RobotLinks")]
public class RobotLink
{
    /// <summary>
    /// 主键标识
    /// 自增主键，唯一标识一条机器人链接记录
    /// </summary>
    [Column(IsPrimary = true, IsIdentity = true)]
    public int Id { get; set; }

    /// <summary>
    /// 用户昵称
    /// 与链接关联的用户昵称
    /// </summary>
    public string NickName { get; set; } = string.Empty;

    /// <summary>
    /// 聊天室链接
    /// 包含用户GUID的聊天室链接地址
    /// </summary>
    public string Link { get; set; } = string.Empty;
}