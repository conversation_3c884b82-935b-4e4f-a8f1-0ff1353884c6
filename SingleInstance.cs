﻿using System.Diagnostics;

namespace OneChatGoRobot;

/// <summary>
/// 单例模式管理类
/// 确保应用程序只能运行一个实例，防止多实例运行导致的资源冲突
///
/// 主要功能：
/// 1. 检测应用程序是否已经在运行
/// 2. 防止用户启动多个程序实例
/// 3. 提供进程间通信机制
/// 4. 支持实例间的消息传递
///
/// 实现原理：
/// - 使用Windows系统的命名事件句柄
/// - 全局唯一的事件名称确保系统级别的单例
/// - 通过事件信号实现实例间通信
///
/// 使用场景：
/// - 聊天室机器人只能运行一个实例
/// - 避免多个实例同时操作数据库
/// - 防止重复的网络连接和消息发送
/// </summary>
public static class SingleInstance
{
    /// <summary>
    /// 程序启动事件句柄
    /// 用于进程间通信，检测应用程序是否已经运行
    ///
    /// 特点：
    /// - 全局命名事件，系统级别唯一
    /// - 支持自动重置模式
    /// - 可以在不同进程间传递信号
    /// - 程序退出时自动释放资源
    /// </summary>
    private static EventWaitHandle? ProgramStarted { get; set; }

    /// <summary>
    /// 应用程序唯一标识名
    /// 用于创建全局唯一的事件句柄，确保应用程序在系统中的唯一性
    ///
    /// 命名规则：
    /// - 使用固定的字符串名称
    /// - 避免与其他应用程序冲突
    /// - 简洁明了，便于识别
    ///
    /// 注意事项：
    /// - 不要随意修改此名称
    /// - 修改后会导致单例检测失效
    /// - 应该在整个应用程序生命周期中保持不变
    /// </summary>
    private const string UniqueAppName = "ChartRoomRobot"; // 固定的唯一字符串名称,用于验证是否为唯一实例

    /// <summary>
    /// 检查应用程序是否应该继续运行
    /// 这是单例检测的核心方法，通过创建或打开命名事件来判断是否已有实例运行
    ///
    /// 工作流程：
    /// 1. 尝试创建全局命名事件
    /// 2. 如果创建成功，说明是第一个实例
    /// 3. 如果创建失败，说明已有实例在运行
    /// 4. 向已运行的实例发送信号
    ///
    /// 返回值说明：
    /// - true：这是第一个实例，应该继续运行
    /// - false：已有实例在运行，当前实例应该退出
    /// </summary>
    /// <returns>如果返回true，表示这是第一个实例，应该继续运行；如果返回false，表示已有实例在运行</returns>
    public static bool IsContinue()
    {
        // 尝试创建一个全局命名的事件句柄
        // 如果事件已存在则打开它，否则创建新的
        // createNew参数会告诉我们是否成功创建了新事件
        ProgramStarted = new EventWaitHandle(false, EventResetMode.AutoReset, UniqueAppName, out bool createNew);

        // 如果事件句柄已存在（即已有实例在运行）
        if (!createNew)
        {
            // 触发事件信号，通知已运行的实例有新的启动尝试
            // 已运行的实例可以通过回调函数响应此信号（如显示窗口等）
            ProgramStarted.Set();
        }

        // 返回是否创建了新的事件句柄
        // true表示这是第一个实例，false表示已有实例在运行
        return createNew;
    }

    /// <summary>
    /// 设置当收到其他实例启动信号时的回调函数
    /// 允许已运行的实例响应新实例的启动尝试
    ///
    /// 使用场景：
    /// - 当用户尝试启动第二个实例时，显示已运行的实例窗口
    /// - 将已运行的实例窗口置于前台
    /// - 执行其他自定义的响应逻辑
    ///
    /// 技术实现：
    /// - 使用线程池注册等待事件
    /// - 当事件被触发时自动执行回调
    /// - 支持传递自定义状态对象
    /// </summary>
    /// <param name="callback">要执行的回调函数，当收到信号时调用</param>
    /// <param name="state">传递给回调函数的状态对象，可以为null</param>
    public static void SetCallback(WaitOrTimerCallback callback, object state)
    {
        // 确保事件句柄已经初始化
        // 这是一个调试时的断言，确保在设置回调前已经调用了IsContinue方法
        Debug.Assert(ProgramStarted != null, nameof(ProgramStarted) + " != null");

        // 在线程池中注册等待事件的回调
        // 参数说明：
        // - ProgramStarted: 要等待的事件句柄
        // - callback: 事件触发时执行的回调函数
        // - state: 传递给回调函数的状态对象
        // - -1: 无限等待（不超时）
        // - false: 不是一次性等待，可以多次触发
        ThreadPool.RegisterWaitForSingleObject(ProgramStarted, callback, state, -1, false);
    }
}